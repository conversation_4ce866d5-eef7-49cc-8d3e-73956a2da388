{"version": 3, "file": "arktype.umd.js", "sources": ["../../node_modules/@standard-schema/utils/dist/index.js", "../src/arktype.ts"], "sourcesContent": ["// src/getDotPath/getDotPath.ts\nfunction getDotPath(issue) {\n  if (issue.path?.length) {\n    let dotPath = \"\";\n    for (const item of issue.path) {\n      const key = typeof item === \"object\" ? item.key : item;\n      if (typeof key === \"string\" || typeof key === \"number\") {\n        if (dotPath) {\n          dotPath += `.${key}`;\n        } else {\n          dotPath += key;\n        }\n      } else {\n        return null;\n      }\n    }\n    return dotPath;\n  }\n  return null;\n}\n\n// src/SchemaError/SchemaError.ts\nvar SchemaError = class extends Error {\n  /**\n   * The schema issues.\n   */\n  issues;\n  /**\n   * Creates a schema error with useful information.\n   *\n   * @param issues The schema issues.\n   */\n  constructor(issues) {\n    super(issues[0].message);\n    this.name = \"SchemaError\";\n    this.issues = issues;\n  }\n};\nexport {\n  SchemaError,\n  getDotPath\n};\n", "import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { StandardSchemaV1 } from '@standard-schema/spec';\nimport { getDotPath } from '@standard-schema/utils';\nimport { FieldError, FieldValues, Resolver } from 'react-hook-form';\n\nfunction parseErrorSchema(\n  issues: readonly StandardSchemaV1.Issue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n\n  for (let i = 0; i < issues.length; i++) {\n    const error = issues[i];\n    const path = getDotPath(error);\n\n    if (path) {\n      if (!errors[path]) {\n        errors[path] = { message: error.message, type: '' };\n      }\n\n      if (validateAllFieldCriteria) {\n        const types = errors[path].types || {};\n\n        errors[path].types = {\n          ...types,\n          [Object.keys(types).length]: error.message,\n        };\n      }\n    }\n  }\n\n  return errors;\n}\n\nexport function arktypeResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions?: {\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function arktypeResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions: never | undefined,\n  resolverOptions: {\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using Arktype schema validation\n * @param {Schema} schema - The Arktype schema to validate against\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {string} [resolverOptions.mode='raw'] - Return the raw input values rather than the parsed values\n * @returns {Resolver<Schema['inferOut']>} A resolver function compatible with react-hook-form\n * @example\n * const schema = type({\n *   username: 'string>2'\n * });\n *\n * useForm({\n *   resolver: arktypeResolver(schema)\n * });\n */\nexport function arktypeResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions: {\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Input | Output> {\n  return async (values: Input, _, options) => {\n    let result = schema['~standard'].validate(values);\n    if (result instanceof Promise) {\n      result = await result;\n    }\n\n    if (result.issues) {\n      const errors = parseErrorSchema(\n        result.issues,\n        !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n      );\n\n      return {\n        values: {},\n        errors: toNestErrors(errors, options),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.raw ? Object.assign({}, values) : result.value,\n      errors: {},\n    };\n  };\n}\n"], "names": ["getDotPath", "issue", "path", "length", "dotPath", "item", "key", "schema", "_schemaOptions", "resolverOptions", "values", "_", "options", "_temp2", "result", "issues", "errors", "validateAllFieldCriteria", "i", "error", "message", "type", "_extends2", "types", "_extends", "Object", "keys", "parseErrorSchema", "shouldUseNativeValidation", "criteriaMode", "toNestErrors", "validateFieldsNatively", "raw", "assign", "value", "validate", "_temp", "Promise", "resolve", "then", "_result", "e", "reject"], "mappings": "qhBACA,SAASA,EAAWC,GAClB,GAAIA,EAAMC,MAAMC,OAAQ,CACtB,IAAIC,EAAU,GACd,IAAK,MAAMC,KAAQJ,EAAMC,KAAM,CAC7B,MAAMI,EAAsB,iBAATD,EAAoBA,EAAKC,IAAMD,EAClD,GAAmB,iBAARC,GAAmC,iBAARA,EAOpC,OAAO,KALLF,GADEA,EACS,IAAIE,IAEJA,CAKhB,CACD,OAAOF,CACR,CACD,OAAO,IACT,mBC8CM,SACJG,EACAC,EACAC,GAIA,gBAJAA,IAAAA,EAEI,CAAE,GAEQC,SAAAA,EAAeC,EAAGC,GAAO,IAAIC,IAAAA,aAMzC,GAAIC,EAAOC,OAAQ,CACjB,IAAMC,EA1EZ,SACED,EACAE,GAIA,IAFA,IAAMD,EAAqC,GAElCE,EAAI,EAAGA,EAAIH,EAAOZ,OAAQe,IAAK,CACtC,IAAMC,EAAQJ,EAAOG,GACfhB,EAAOF,EAAWmB,GAExB,GAAIjB,IACGc,EAAOd,KACVc,EAAOd,GAAQ,CAAEkB,QAASD,EAAMC,QAASC,KAAM,KAG7CJ,GAA0B,CAAAK,IAAAA,EACtBC,EAAQP,EAAOd,GAAMqB,OAAS,GAEpCP,EAAOd,GAAMqB,MAAKC,KACbD,IAAKD,EAAA,CAAA,GACPG,OAAOC,KAAKH,GAAOpB,QAASgB,EAAMC,QAAOE,GAE9C,CAEJ,CAEA,OAAON,CACT,CA+CqBW,CACbb,EAAOC,QACNH,EAAQgB,2BAAsD,QAAzBhB,EAAQiB,cAGhD,MAAO,CACLnB,OAAQ,CAAA,EACRM,OAAQc,eAAad,EAAQJ,GAEjC,CAIA,OAFAA,EAAQgB,2BAA6BG,EAAAA,uBAAuB,CAAA,EAAInB,GAEzD,CACLF,OAAQD,EAAgBuB,IAAMP,OAAOQ,OAAO,CAAA,EAAIvB,GAAUI,EAAOoB,MACjElB,OAAQ,CAAA,EACR,EAtBEF,EAASP,EAAO,aAAa4B,SAASzB,GAAQ0B,gBAC9CtB,aAAkBuB,QAAO,OAAAA,QAAAC,QACZxB,GAAMyB,KAAA,SAAAC,GAArB1B,EAAM0B,CAAgB,EAAAH,IAAAA,OAAAA,QAAAC,QAAAF,GAAAA,EAAAG,KAAAH,EAAAG,KAAA1B,GAAAA,IAqB1B,CAAC,MAAA4B,GAAAJ,OAAAA,QAAAK,OAAAD,EACH,CAAA,CAAA"}