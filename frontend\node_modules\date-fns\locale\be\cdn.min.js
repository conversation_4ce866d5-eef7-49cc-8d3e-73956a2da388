(()=>{var A;function j(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);C&&(J=J.filter(function(Z){return Object.getOwnPropertyDescriptor(B,Z).enumerable})),G.push.apply(G,J)}return G}function M(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?j(Object(G),!0).forEach(function(J){v(B,J,G[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):j(Object(G)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(G,J))})}return B}function v(B,C,G){if(C=F(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function F(B){var C=f(B,"string");return E(C)=="symbol"?C:String(C)}function f(B,C){if(E(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var J=G.call(B,C||"default");if(E(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}function b(B,C){return g(B)||_(B,C)||k(B,C)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function k(B,C){if(!B)return;if(typeof B==="string")return W(B,C);var G=Object.prototype.toString.call(B).slice(8,-1);if(G==="Object"&&B.constructor)G=B.constructor.name;if(G==="Map"||G==="Set")return Array.from(B);if(G==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(G))return W(B,C)}function W(B,C){if(C==null||C>B.length)C=B.length;for(var G=0,J=new Array(C);G<C;G++)J[G]=B[G];return J}function _(B,C){var G=B==null?null:typeof Symbol!="undefined"&&B[Symbol.iterator]||B["@@iterator"];if(G!=null){var J,Z,X,U,H=[],q=!0,Y=!1;try{if(X=(G=G.call(B)).next,C===0){if(Object(G)!==G)return;q=!1}else for(;!(q=(J=X.call(G)).done)&&(H.push(J.value),H.length!==C);q=!0);}catch(K){Y=!0,Z=K}finally{try{if(!q&&G.return!=null&&(U=G.return(),Object(U)!==U))return}finally{if(Y)throw Z}}return H}}function g(B){if(Array.isArray(B))return B}function E(B){return E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},E(B)}var y=Object.defineProperty,fB=function B(C,G){for(var J in G)y(C,J,{get:G[J],enumerable:!0,configurable:!0,set:function Z(X){return G[J]=function(){return X}}})};function V(B,C){if(B.one!==void 0&&C===1)return B.one;var G=C%10,J=C%100;if(G===1&&J!==11)return B.singularNominative.replace("{{count}}",String(C));else if(G>=2&&G<=4&&(J<10||J>20))return B.singularGenitive.replace("{{count}}",String(C));else return B.pluralGenitive.replace("{{count}}",String(C))}function Q(B){return function(C,G){if(G&&G.addSuffix)if(G.comparison&&G.comparison>0)if(B.future)return V(B.future,C);else return"\u043F\u0440\u0430\u0437 "+V(B.regular,C);else if(B.past)return V(B.past,C);else return V(B.regular,C)+" \u0442\u0430\u043C\u0443";else return V(B.regular,C)}}var m=function B(C,G){if(G&&G.addSuffix)if(G.comparison&&G.comparison>0)return"\u043F\u0440\u0430\u0437 \u043F\u0430\u045E\u0445\u0432\u0456\u043B\u0456\u043D\u044B";else return"\u043F\u0430\u045E\u0445\u0432\u0456\u043B\u0456\u043D\u044B \u0442\u0430\u043C\u0443";return"\u043F\u0430\u045E\u0445\u0432\u0456\u043B\u0456\u043D\u044B"},c={lessThanXSeconds:Q({regular:{one:"\u043C\u0435\u043D\u0448 \u0437\u0430 \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularNominative:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},future:{one:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularNominative:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),xSeconds:Q({regular:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0430",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},past:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443 \u0442\u0430\u043C\u0443",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B \u0442\u0430\u043C\u0443",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0442\u0430\u043C\u0443"},future:{singularNominative:"\u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),halfAMinute:m,lessThanXMinutes:Q({regular:{one:"\u043C\u0435\u043D\u0448 \u0437\u0430 \u0445\u0432\u0456\u043B\u0456\u043D\u0443",singularNominative:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u0443",singularGenitive:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u044B",pluralGenitive:"\u043C\u0435\u043D\u0448 \u0437\u0430 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D"},future:{one:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 \u0445\u0432\u0456\u043B\u0456\u043D\u0443",singularNominative:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u0443",singularGenitive:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u044B",pluralGenitive:"\u043C\u0435\u043D\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D"}}),xMinutes:Q({regular:{singularNominative:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u0430",singularGenitive:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u044B",pluralGenitive:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D"},past:{singularNominative:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u0443 \u0442\u0430\u043C\u0443",singularGenitive:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u044B \u0442\u0430\u043C\u0443",pluralGenitive:"{{count}} \u0445\u0432\u0456\u043B\u0456\u043D \u0442\u0430\u043C\u0443"},future:{singularNominative:"\u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u0443",singularGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D\u044B",pluralGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0445\u0432\u0456\u043B\u0456\u043D"}}),aboutXHours:Q({regular:{singularNominative:"\u043A\u0430\u043B\u044F {{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u044B",singularGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0433\u0430\u0434\u0437\u0456\u043D",pluralGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0433\u0430\u0434\u0437\u0456\u043D"},future:{singularNominative:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u0443",singularGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u044B",pluralGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D"}}),xHours:Q({regular:{singularNominative:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u0430",singularGenitive:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u044B",pluralGenitive:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D"},past:{singularNominative:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u0443 \u0442\u0430\u043C\u0443",singularGenitive:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u044B \u0442\u0430\u043C\u0443",pluralGenitive:"{{count}} \u0433\u0430\u0434\u0437\u0456\u043D \u0442\u0430\u043C\u0443"},future:{singularNominative:"\u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u0443",singularGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D\u044B",pluralGenitive:"\u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u0437\u0456\u043D"}}),xDays:Q({regular:{singularNominative:"{{count}} \u0434\u0437\u0435\u043D\u044C",singularGenitive:"{{count}} \u0434\u043D\u0456",pluralGenitive:"{{count}} \u0434\u0437\u0451\u043D"}}),aboutXWeeks:Q({regular:{singularNominative:"\u043A\u0430\u043B\u044F {{count}} \u0442\u044B\u0434\u043D\u0456",singularGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0442\u044B\u0434\u043D\u044F\u045E",pluralGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0442\u044B\u0434\u043D\u044F\u045E"},future:{singularNominative:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0442\u044B\u0434\u0437\u0435\u043D\u044C",singularGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0442\u044B\u0434\u043D\u0456",pluralGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0442\u044B\u0434\u043D\u044F\u045E"}}),xWeeks:Q({regular:{singularNominative:"{{count}} \u0442\u044B\u0434\u0437\u0435\u043D\u044C",singularGenitive:"{{count}} \u0442\u044B\u0434\u043D\u0456",pluralGenitive:"{{count}} \u0442\u044B\u0434\u043D\u044F\u045E"}}),aboutXMonths:Q({regular:{singularNominative:"\u043A\u0430\u043B\u044F {{count}} \u043C\u0435\u0441\u044F\u0446\u0430",singularGenitive:"\u043A\u0430\u043B\u044F {{count}} \u043C\u0435\u0441\u044F\u0446\u0430\u045E",pluralGenitive:"\u043A\u0430\u043B\u044F {{count}} \u043C\u0435\u0441\u044F\u0446\u0430\u045E"},future:{singularNominative:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446",singularGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446\u044B",pluralGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446\u0430\u045E"}}),xMonths:Q({regular:{singularNominative:"{{count}} \u043C\u0435\u0441\u044F\u0446",singularGenitive:"{{count}} \u043C\u0435\u0441\u044F\u0446\u044B",pluralGenitive:"{{count}} \u043C\u0435\u0441\u044F\u0446\u0430\u045E"}}),aboutXYears:Q({regular:{singularNominative:"\u043A\u0430\u043B\u044F {{count}} \u0433\u043E\u0434\u0430",singularGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0433\u0430\u0434\u043E\u045E",pluralGenitive:"\u043A\u0430\u043B\u044F {{count}} \u0433\u0430\u0434\u043E\u045E"},future:{singularNominative:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"\u043F\u0440\u044B\u0431\u043B\u0456\u0437\u043D\u0430 \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u043E\u045E"}}),xYears:Q({regular:{singularNominative:"{{count}} \u0433\u043E\u0434",singularGenitive:"{{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"{{count}} \u0433\u0430\u0434\u043E\u045E"}}),overXYears:Q({regular:{singularNominative:"\u0431\u043E\u043B\u044C\u0448 \u0437\u0430 {{count}} \u0433\u043E\u0434",singularGenitive:"\u0431\u043E\u043B\u044C\u0448 \u0437\u0430 {{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"\u0431\u043E\u043B\u044C\u0448 \u0437\u0430 {{count}} \u0433\u0430\u0434\u043E\u045E"},future:{singularNominative:"\u0431\u043E\u043B\u044C\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u0431\u043E\u043B\u044C\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"\u0431\u043E\u043B\u044C\u0448, \u0447\u044B\u043C \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u043E\u045E"}}),almostXYears:Q({regular:{singularNominative:"\u0430\u043C\u0430\u043B\u044C {{count}} \u0433\u043E\u0434",singularGenitive:"\u0430\u043C\u0430\u043B\u044C {{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"\u0430\u043C\u0430\u043B\u044C {{count}} \u0433\u0430\u0434\u043E\u045E"},future:{singularNominative:"\u0430\u043C\u0430\u043B\u044C \u043F\u0440\u0430\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u0430\u043C\u0430\u043B\u044C \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u044B",pluralGenitive:"\u0430\u043C\u0430\u043B\u044C \u043F\u0440\u0430\u0437 {{count}} \u0433\u0430\u0434\u043E\u045E"}})},p=function B(C,G,J){return J=J||{},c[C](G,J)};function x(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,J=B.formats[G]||B.formats[B.defaultWidth];return J}}var u={full:"EEEE, d MMMM y '\u0433.'",long:"d MMMM y '\u0433.'",medium:"d MMM y '\u0433.'",short:"dd.MM.y"},d={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},l={any:"{{date}}, {{time}}"},i={date:x({formats:u,defaultWidth:"full"}),time:x({formats:d,defaultWidth:"full"}),dateTime:x({formats:l,defaultWidth:"any"})},bB=7,s=365.2425,r=Math.pow(10,8)*24*60*60*1000,hB=-r,kB=604800000,_B=86400000,gB=60000,yB=3600000,mB=1000,cB=525600,pB=43200,uB=1440,dB=60,lB=3,iB=12,sB=4,n=3600,rB=60,O=n*24,nB=O*7,o=O*s,a=o/12,oB=a*3,T=Symbol.for("constructDateFrom");function P(B,C){if(typeof B==="function")return B(C);if(B&&E(B)==="object"&&T in B)return B[T](C);if(B instanceof Date)return new B.constructor(C);return new Date(C)}function t(B){for(var C=arguments.length,G=new Array(C>1?C-1:0),J=1;J<C;J++)G[J-1]=arguments[J];var Z=P.bind(null,B||G.find(function(X){return E(X)==="object"}));return G.map(Z)}function e(){return z}function aB(B){z=B}var z={};function S(B,C){return P(C||B,B)}function w(B,C){var G,J,Z,X,U,H,q=e(),Y=(G=(J=(Z=(X=C===null||C===void 0?void 0:C.weekStartsOn)!==null&&X!==void 0?X:C===null||C===void 0||(U=C.locale)===null||U===void 0||(U=U.options)===null||U===void 0?void 0:U.weekStartsOn)!==null&&Z!==void 0?Z:q.weekStartsOn)!==null&&J!==void 0?J:(H=q.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&G!==void 0?G:0,K=S(B,C===null||C===void 0?void 0:C.in),N=K.getDay(),FB=(N<Y?7:0)+N-Y;return K.setDate(K.getDate()-FB),K.setHours(0,0,0,0),K}function D(B,C,G){var J=t(G===null||G===void 0?void 0:G.in,B,C),Z=b(J,2),X=Z[0],U=Z[1];return+w(X,G)===+w(U,G)}function BB(B){var C=$[B];switch(B){case 0:case 3:case 5:case 6:return"'\u0443 \u043C\u0456\u043D\u0443\u043B\u0443\u044E "+C+" \u0430' p";case 1:case 2:case 4:return"'\u0443 \u043C\u0456\u043D\u0443\u043B\u044B "+C+" \u0430' p"}}function L(B){var C=$[B];return"'\u0443 "+C+" \u0430' p"}function CB(B){var C=$[B];switch(B){case 0:case 3:case 5:case 6:return"'\u0443 \u043D\u0430\u0441\u0442\u0443\u043F\u043D\u0443\u044E "+C+" \u0430' p";case 1:case 2:case 4:return"'\u0443 \u043D\u0430\u0441\u0442\u0443\u043F\u043D\u044B "+C+" \u0430' p"}}var $=["\u043D\u044F\u0434\u0437\u0435\u043B\u044E","\u043F\u0430\u043D\u044F\u0434\u0437\u0435\u043B\u0430\u043A","\u0430\u045E\u0442\u043E\u0440\u0430\u043A","\u0441\u0435\u0440\u0430\u0434\u0443","\u0447\u0430\u0446\u0432\u0435\u0440","\u043F\u044F\u0442\u043D\u0456\u0446\u0443","\u0441\u0443\u0431\u043E\u0442\u0443"],GB=function B(C,G,J){var Z=S(C),X=Z.getDay();if(D(Z,G,J))return L(X);else return BB(X)},JB=function B(C,G,J){var Z=S(C),X=Z.getDay();if(D(Z,G,J))return L(X);else return CB(X)},XB={lastWeek:GB,yesterday:"'\u0443\u0447\u043E\u0440\u0430 \u0430' p",today:"'\u0441\u0451\u043D\u043D\u044F \u0430' p",tomorrow:"'\u0437\u0430\u045E\u0442\u0440\u0430 \u0430' p",nextWeek:JB,other:"P"},ZB=function B(C,G,J,Z){var X=XB[C];if(typeof X==="function")return X(G,J,Z);return X};function I(B){return function(C,G){var J=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",Z;if(J==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,U=G!==null&&G!==void 0&&G.width?String(G.width):X;Z=B.formattingValues[U]||B.formattingValues[X]}else{var H=B.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;Z=B.values[q]||B.values[H]}var Y=B.argumentCallback?B.argumentCallback(C):C;return Z[Y]}}var UB={narrow:["\u0434\u0430 \u043D.\u044D.","\u043D.\u044D."],abbreviated:["\u0434\u0430 \u043D. \u044D.","\u043D. \u044D."],wide:["\u0434\u0430 \u043D\u0430\u0448\u0430\u0439 \u044D\u0440\u044B","\u043D\u0430\u0448\u0430\u0439 \u044D\u0440\u044B"]},HB={narrow:["1","2","3","4"],abbreviated:["1-\u044B \u043A\u0432.","2-\u0456 \u043A\u0432.","3-\u0456 \u043A\u0432.","4-\u044B \u043A\u0432."],wide:["1-\u044B \u043A\u0432\u0430\u0440\u0442\u0430\u043B","2-\u0456 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","3-\u0456 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","4-\u044B \u043A\u0432\u0430\u0440\u0442\u0430\u043B"]},QB={narrow:["\u0421","\u041B","\u0421","\u041A","\u041C","\u0427","\u041B","\u0416","\u0412","\u041A","\u041B","\u0421"],abbreviated:["\u0441\u0442\u0443\u0434\u0437.","\u043B\u044E\u0442.","\u0441\u0430\u043A.","\u043A\u0440\u0430\u0441.","\u043C\u0430\u0439","\u0447\u044D\u0440\u0432.","\u043B\u0456\u043F.","\u0436\u043D.","\u0432\u0435\u0440.","\u043A\u0430\u0441\u0442\u0440.","\u043B\u0456\u0441\u0442.","\u0441\u043D\u0435\u0436."],wide:["\u0441\u0442\u0443\u0434\u0437\u0435\u043D\u044C","\u043B\u044E\u0442\u044B","\u0441\u0430\u043A\u0430\u0432\u0456\u043A","\u043A\u0440\u0430\u0441\u0430\u0432\u0456\u043A","\u043C\u0430\u0439","\u0447\u044D\u0440\u0432\u0435\u043D\u044C","\u043B\u0456\u043F\u0435\u043D\u044C","\u0436\u043D\u0456\u0432\u0435\u043D\u044C","\u0432\u0435\u0440\u0430\u0441\u0435\u043D\u044C","\u043A\u0430\u0441\u0442\u0440\u044B\u0447\u043D\u0456\u043A","\u043B\u0456\u0441\u0442\u0430\u043F\u0430\u0434","\u0441\u043D\u0435\u0436\u0430\u043D\u044C"]},YB={narrow:["\u0421","\u041B","\u0421","\u041A","\u041C","\u0427","\u041B","\u0416","\u0412","\u041A","\u041B","\u0421"],abbreviated:["\u0441\u0442\u0443\u0434\u0437.","\u043B\u044E\u0442.","\u0441\u0430\u043A.","\u043A\u0440\u0430\u0441.","\u043C\u0430\u044F","\u0447\u044D\u0440\u0432.","\u043B\u0456\u043F.","\u0436\u043D.","\u0432\u0435\u0440.","\u043A\u0430\u0441\u0442\u0440.","\u043B\u0456\u0441\u0442.","\u0441\u043D\u0435\u0436."],wide:["\u0441\u0442\u0443\u0434\u0437\u0435\u043D\u044F","\u043B\u044E\u0442\u0430\u0433\u0430","\u0441\u0430\u043A\u0430\u0432\u0456\u043A\u0430","\u043A\u0440\u0430\u0441\u0430\u0432\u0456\u043A\u0430","\u043C\u0430\u044F","\u0447\u044D\u0440\u0432\u0435\u043D\u044F","\u043B\u0456\u043F\u0435\u043D\u044F","\u0436\u043D\u0456\u045E\u043D\u044F","\u0432\u0435\u0440\u0430\u0441\u043D\u044F","\u043A\u0430\u0441\u0442\u0440\u044B\u0447\u043D\u0456\u043A\u0430","\u043B\u0456\u0441\u0442\u0430\u043F\u0430\u0434\u0430","\u0441\u043D\u0435\u0436\u043D\u044F"]},qB={narrow:["\u041D","\u041F","\u0410","\u0421","\u0427","\u041F","\u0421"],short:["\u043D\u0434","\u043F\u043D","\u0430\u045E","\u0441\u0440","\u0447\u0446","\u043F\u0442","\u0441\u0431"],abbreviated:["\u043D\u044F\u0434\u0437","\u043F\u0430\u043D","\u0430\u045E\u0442","\u0441\u0435\u0440","\u0447\u0430\u0446","\u043F\u044F\u0442","\u0441\u0443\u0431"],wide:["\u043D\u044F\u0434\u0437\u0435\u043B\u044F","\u043F\u0430\u043D\u044F\u0434\u0437\u0435\u043B\u0430\u043A","\u0430\u045E\u0442\u043E\u0440\u0430\u043A","\u0441\u0435\u0440\u0430\u0434\u0430","\u0447\u0430\u0446\u0432\u0435\u0440","\u043F\u044F\u0442\u043D\u0456\u0446\u0430","\u0441\u0443\u0431\u043E\u0442\u0430"]},KB={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D.",noon:"\u043F\u043E\u045E\u0434.",morning:"\u0440\u0430\u043D.",afternoon:"\u0434\u0437\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D.",noon:"\u043F\u043E\u045E\u0434.",morning:"\u0440\u0430\u043D.",afternoon:"\u0434\u0437\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D\u0430\u0447",noon:"\u043F\u043E\u045E\u0434\u0437\u0435\u043D\u044C",morning:"\u0440\u0430\u043D\u0456\u0446\u0430",afternoon:"\u0434\u0437\u0435\u043D\u044C",evening:"\u0432\u0435\u0447\u0430\u0440",night:"\u043D\u043E\u0447"}},EB={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D.",noon:"\u043F\u043E\u045E\u0434.",morning:"\u0440\u0430\u043D.",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u044B"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D.",noon:"\u043F\u043E\u045E\u0434.",morning:"\u0440\u0430\u043D.",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u044B"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u045E\u043D\u0430\u0447",noon:"\u043F\u043E\u045E\u0434\u0437\u0435\u043D\u044C",morning:"\u0440\u0430\u043D\u0456\u0446\u044B",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447\u0430\u0440\u0430",night:"\u043D\u043E\u0447\u044B"}},NB=function B(C,G){var J=String(G===null||G===void 0?void 0:G.unit),Z=Number(C),X;if(J==="date")X="-\u0433\u0430";else if(J==="hour"||J==="minute"||J==="second")X="-\u044F";else X=(Z%10===2||Z%10===3)&&Z%100!==12&&Z%100!==13?"-\u0456":"-\u044B";return Z+X},VB={ordinalNumber:NB,era:I({values:UB,defaultWidth:"wide"}),quarter:I({values:HB,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:I({values:QB,defaultWidth:"wide",formattingValues:YB,defaultFormattingWidth:"wide"}),day:I({values:qB,defaultWidth:"wide"}),dayPeriod:I({values:KB,defaultWidth:"any",formattingValues:EB,defaultFormattingWidth:"wide"})};function R(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.width,Z=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],X=C.match(Z);if(!X)return null;var U=X[0],H=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(H)?RB(H,function(N){return N.test(U)}):IB(H,function(N){return N.test(U)}),Y;Y=B.valueCallback?B.valueCallback(q):q,Y=G.valueCallback?G.valueCallback(Y):Y;var K=C.slice(U.length);return{value:Y,rest:K}}}function IB(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function RB(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function MB(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=C.match(B.matchPattern);if(!J)return null;var Z=J[0],X=C.match(B.parsePattern);if(!X)return null;var U=B.valueCallback?B.valueCallback(X[0]):X[0];U=G.valueCallback?G.valueCallback(U):U;var H=C.slice(Z.length);return{value:U,rest:H}}}var AB=/^(\d+)(-?(е|я|га|і|ы|ае|ая|яя|шы|гі|ці|ты|мы))?/i,xB=/\d+/i,SB={narrow:/^((да )?н\.?\s?э\.?)/i,abbreviated:/^((да )?н\.?\s?э\.?)/i,wide:/^(да нашай эры|нашай эры|наша эра)/i},$B={any:[/^д/i,/^н/i]},jB={narrow:/^[1234]/i,abbreviated:/^[1234](-?[ыі]?)? кв.?/i,wide:/^[1234](-?[ыі]?)? квартал/i},WB={any:[/1/i,/2/i,/3/i,/4/i]},OB={narrow:/^[слкмчжв]/i,abbreviated:/^(студз|лют|сак|крас|ма[йя]|чэрв|ліп|жн|вер|кастр|ліст|снеж)\.?/i,wide:/^(студзен[ья]|лют(ы|ага)|сакавіка?|красавіка?|ма[йя]|чэрвен[ья]|ліпен[ья]|жні(вень|ўня)|верас(ень|ня)|кастрычніка?|лістапада?|снеж(ань|ня))/i},TB={narrow:[/^с/i,/^л/i,/^с/i,/^к/i,/^м/i,/^ч/i,/^л/i,/^ж/i,/^в/i,/^к/i,/^л/i,/^с/i],any:[/^ст/i,/^лю/i,/^са/i,/^кр/i,/^ма/i,/^ч/i,/^ліп/i,/^ж/i,/^в/i,/^ка/i,/^ліс/i,/^сн/i]},PB={narrow:/^[нпасч]/i,short:/^(нд|ня|пн|па|аў|ат|ср|се|чц|ча|пт|пя|сб|су)\.?/i,abbreviated:/^(нядз?|ндз|пнд|пан|аўт|срд|сер|чцв|чац|птн|пят|суб).?/i,wide:/^(нядзел[яі]|панядзел(ак|ка)|аўтор(ак|ка)|серад[аы]|чацв(ер|ярга)|пятніц[аы]|субот[аы])/i},zB={narrow:[/^н/i,/^п/i,/^а/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н/i,/^п[ан]/i,/^а/i,/^с[ер]/i,/^ч/i,/^п[ят]/i,/^с[уб]/i]},wB={narrow:/^([дп]п|поўн\.?|поўд\.?|ран\.?|дзень|дня|веч\.?|ночы?)/i,abbreviated:/^([дп]п|поўн\.?|поўд\.?|ран\.?|дзень|дня|веч\.?|ночы?)/i,wide:/^([дп]п|поўнач|поўдзень|раніц[аы]|дзень|дня|вечара?|ночы?)/i},DB={any:{am:/^дп/i,pm:/^пп/i,midnight:/^поўн/i,noon:/^поўд/i,morning:/^р/i,afternoon:/^д[зн]/i,evening:/^в/i,night:/^н/i}},LB={ordinalNumber:MB({matchPattern:AB,parsePattern:xB,valueCallback:function B(C){return parseInt(C,10)}}),era:R({matchPatterns:SB,defaultMatchWidth:"wide",parsePatterns:$B,defaultParseWidth:"any"}),quarter:R({matchPatterns:jB,defaultMatchWidth:"wide",parsePatterns:WB,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:R({matchPatterns:OB,defaultMatchWidth:"wide",parsePatterns:TB,defaultParseWidth:"any"}),day:R({matchPatterns:PB,defaultMatchWidth:"wide",parsePatterns:zB,defaultParseWidth:"any"}),dayPeriod:R({matchPatterns:wB,defaultMatchWidth:"wide",parsePatterns:DB,defaultParseWidth:"any"})},vB={code:"be",formatDistance:p,formatLong:i,formatRelative:ZB,localize:VB,match:LB,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=M(M({},window.dateFns),{},{locale:M(M({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{be:vB})})})();

//# debugId=302D5201FDDCDB3864756E2164756E21
