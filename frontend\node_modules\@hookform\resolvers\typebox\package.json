{"name": "@hookform/resolvers/typebox", "amdName": "hookformResolversTypebox", "version": "1.0.0", "private": true, "description": "React Hook Form validation resolver: typebox", "main": "dist/typebox.js", "module": "dist/typebox.module.js", "umd:main": "dist/typebox.umd.js", "source": "src/index.ts", "types": "dist/index.d.ts", "license": "MIT", "peerDependencies": {"react-hook-form": "^7.55.0", "@hookform/resolvers": "^2.0.0", "@sinclair/typebox": "^0.25.24"}}