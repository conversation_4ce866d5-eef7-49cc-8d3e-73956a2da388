"use strict";
exports.hy = void 0;
var _index = require("./hy/_lib/formatDistance.cjs");
var _index2 = require("./hy/_lib/formatLong.cjs");
var _index3 = require("./hy/_lib/formatRelative.cjs");
var _index4 = require("./hy/_lib/localize.cjs");
var _index5 = require("./hy/_lib/match.cjs");

/**
 * @category Locales
 * @summary Armenian locale
 * @language Armenian
 * @iso-639-2 arm
 * <AUTHOR> [@alexigityan](https://github.com/alexigityan)
 */
const hy = (exports.hy = {
  code: "hy",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
