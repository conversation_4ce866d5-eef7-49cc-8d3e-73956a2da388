{"version": 3, "sources": ["style.css"], "names": [], "mappings": "AAAA;EACE,qBAAqB,EAAE,2BAA2B;EAClD,6BAA6B,EAAE,sCAAsC;EACrE,2BAA2B,EAAE,sDAAsD;EACnF,+BAA+B,EAAE,uDAAuD;EACxF,gCAAgC,EAAE,4EAA4E;EAC9G,oCAAoC,EAAE,6EAA6E;EACnH,gDAAgD,EAAE,wCAAwC;EAC1F,yDAAyD,EAAE,uDAAuD;EAClH,0BAA0B,EAAE,+BAA+B;;EAE3D,WAAW;AACb;;AAEA,0DAA0D;AAC1D;EACE,sBAAsB;EACtB,UAAU;EACV,SAAS;EACT,uBAAuB;EACvB,SAAS;EACT,qBAAqB;EACrB,wBAAwB;EACxB,gBAAgB;EAChB,6BAA6B;EAC7B,MAAM;EACN,qBAAqB;EACrB,sBAAsB;EACtB,qBAAqB;EACrB,2BAA2B;EAC3B,yCAAyC;EACzC,oBAAoB;AACtB;;AAEA,YAAY;AACZ;EACE,gBAAgB;EAChB,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,eAAe;EACf,cAAc;EACd,gBAAgB;EAChB,aAAa;;EAEb,qBAAqB;EACrB,wBAAwB;AAC1B;;AAEA;EACE,qEAAqE;EACrE,aAAa;AACf;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,cAAc;EACd,6CAA6C;EAC7C,0BAA0B;AAC5B;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,SAAS;EACT,yCAAyC;EACzC,yBAAyB;AAC3B;;AAEA;EACE,yCAAyC;EACzC,yBAAyB;AAC3B;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;AAClB;;AAEA;EACE,kBAAkB;EAClB,cAAc;EACd,kBAAkB;AACpB;;AAEA;EACE,kBAAkB;EAClB,oBAAoB;AACtB;;AAEA;EACE,kBAAkB;EAClB,UAAU;EACV,oBAAoB;EACpB,mBAAmB;EACnB,SAAS;EACT,iBAAiB;EACjB,mBAAmB;EACnB,mBAAmB;EACnB,SAAS;EACT,6BAA6B;EAC7B,oBAAoB;EACpB,uCAAuC;EACvC,iBAAiB;AACnB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,kBAAkB;EAClB,QAAQ;EACR,OAAO;EACP,2BAA2B;AAC7B;;AAEA;EACE,kBAAkB;EAClB,QAAQ;EACR,QAAQ;EACR,2BAA2B;AAC7B;;AAEA;EACE,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;EACvB,2BAA2B;EAC3B,4BAA4B;EAC5B,eAAe;EACf,mBAAmB;AACrB;;AAEA,eAAe;AACf,eAAe;AACf,eAAe;;AAEf;;EAEE,kBAAkB;EAClB,oBAAoB;EACpB,mBAAmB;AACrB;;AAEA;EACE,gBAAgB;EAChB,kBAAkB;EAClB,UAAU;EACV,MAAM;EACN,SAAS;EACT,OAAO;EACP,WAAW;EACX,SAAS;EACT,UAAU;EACV,eAAe;EACf,UAAU;EACV,YAAY;EACZ,6BAA6B;EAC7B,oBAAoB;EACpB,kBAAkB;EAClB,oBAAoB;AACtB;;AAEA;EACE,cAAc;EACd,YAAY;AACd;;AAEA;EACE,6CAA6C;EAC7C,0BAA0B;EAC1B,kBAAkB;AACpB;;AAEA;EACE,iBAAiB;AACnB;;AAEA;EACE,SAAS;AACX;;AAEA;;EAEE,YAAY;AACd;;AAEA;EACE,sBAAsB;EACtB,iBAAiB;EACjB,gBAAgB;EAChB,kBAAkB;EAClB,YAAY;EACZ,4BAA4B;EAC5B,UAAU;EACV,yBAAyB;AAC3B;;AAEA;EACE,SAAS;AACX;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,2BAA2B;EAC3B,YAAY;EACZ,4BAA4B;EAC5B,UAAU;EACV,kBAAkB;AACpB;;AAEA;EACE,iBAAiB;AACnB;;AAEA;;EAEE,aAAa;EACb,gBAAgB;EAChB,mBAAmB;EACnB,uBAAuB;EACvB,sBAAsB;EACtB,2BAA2B;EAC3B,+BAA+B;EAC/B,4BAA4B;EAC5B,SAAS;EACT,6BAA6B;EAC7B,mBAAmB;AACrB;;AAEA;EACE,iBAAiB;AACnB;;AAEA;;;EAGE,gCAAgC;EAChC,UAAU;EACV,yCAAyC;AAC3C;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,2DAA2D;EAC3D,2BAA2B;EAC3B,mBAAmB;EACnB,UAAU;AACZ;;AAEA;EACE,0BAA0B;EAC1B,6BAA6B;AAC/B;;AAEA;EACE,yBAAyB;EACzB,4BAA4B;AAC9B;;AAEA;EACE,yBAAyB;EACzB,4BAA4B;AAC9B;;AAEA;EACE,0BAA0B;EAC1B,6BAA6B;AAC/B;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,gBAAgB;AAClB", "file": "style.css", "sourcesContent": [".rdp {\n  --rdp-cell-size: 40px; /* Size of the day cells. */\n  --rdp-caption-font-size: 18px; /* Font size for the caption labels. */\n  --rdp-accent-color: #0000ff; /* Accent color for the background of selected days. */\n  --rdp-background-color: #e7edff; /* Background color for the hovered/focused elements. */\n  --rdp-accent-color-dark: #3003e1; /* Accent color for the background of selected days (to use in dark-mode). */\n  --rdp-background-color-dark: #180270; /* Background color for the hovered/focused elements (to use in dark-mode). */\n  --rdp-outline: 2px solid var(--rdp-accent-color); /* Outline border for focused elements */\n  --rdp-outline-selected: 3px solid var(--rdp-accent-color); /* Outline border for focused _and_ selected elements */\n  --rdp-selected-color: #fff; /* Color of selected day text */\n\n  margin: 1em;\n}\n\n/* Hide elements for devices that are not screen readers */\n.rdp-vhidden {\n  box-sizing: border-box;\n  padding: 0;\n  margin: 0;\n  background: transparent;\n  border: 0;\n  -moz-appearance: none;\n  -webkit-appearance: none;\n  appearance: none;\n  position: absolute !important;\n  top: 0;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  overflow: hidden !important;\n  clip: rect(1px, 1px, 1px, 1px) !important;\n  border: 0 !important;\n}\n\n/* Buttons */\n.rdp-button_reset {\n  appearance: none;\n  position: relative;\n  margin: 0;\n  padding: 0;\n  cursor: default;\n  color: inherit;\n  background: none;\n  font: inherit;\n\n  -moz-appearance: none;\n  -webkit-appearance: none;\n}\n\n.rdp-button_reset:focus-visible {\n  /* Make sure to reset outline only when :focus-visible is supported */\n  outline: none;\n}\n\n.rdp-button {\n  border: 2px solid transparent;\n}\n\n.rdp-button[disabled]:not(.rdp-day_selected) {\n  opacity: 0.25;\n}\n\n.rdp-button:not([disabled]) {\n  cursor: pointer;\n}\n\n.rdp-button:focus-visible:not([disabled]) {\n  color: inherit;\n  background-color: var(--rdp-background-color);\n  border: var(--rdp-outline);\n}\n\n.rdp-button:hover:not([disabled]):not(.rdp-day_selected) {\n  background-color: var(--rdp-background-color);\n}\n\n.rdp-months {\n  display: flex;\n}\n\n.rdp-month {\n  margin: 0 1em;\n}\n\n.rdp-month:first-child {\n  margin-left: 0;\n}\n\n.rdp-month:last-child {\n  margin-right: 0;\n}\n\n.rdp-table {\n  margin: 0;\n  max-width: calc(var(--rdp-cell-size) * 7);\n  border-collapse: collapse;\n}\n\n.rdp-with_weeknumber .rdp-table {\n  max-width: calc(var(--rdp-cell-size) * 8);\n  border-collapse: collapse;\n}\n\n.rdp-caption {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0;\n  text-align: left;\n}\n\n.rdp-multiple_months .rdp-caption {\n  position: relative;\n  display: block;\n  text-align: center;\n}\n\n.rdp-caption_dropdowns {\n  position: relative;\n  display: inline-flex;\n}\n\n.rdp-caption_label {\n  position: relative;\n  z-index: 1;\n  display: inline-flex;\n  align-items: center;\n  margin: 0;\n  padding: 0 0.25em;\n  white-space: nowrap;\n  color: currentColor;\n  border: 0;\n  border: 2px solid transparent;\n  font-family: inherit;\n  font-size: var(--rdp-caption-font-size);\n  font-weight: bold;\n}\n\n.rdp-nav {\n  white-space: nowrap;\n}\n\n.rdp-multiple_months .rdp-caption_start .rdp-nav {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  transform: translateY(-50%);\n}\n\n.rdp-multiple_months .rdp-caption_end .rdp-nav {\n  position: absolute;\n  top: 50%;\n  right: 0;\n  transform: translateY(-50%);\n}\n\n.rdp-nav_button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: var(--rdp-cell-size);\n  height: var(--rdp-cell-size);\n  padding: 0.25em;\n  border-radius: 100%;\n}\n\n/* ---------- */\n/* Dropdowns  */\n/* ---------- */\n\n.rdp-dropdown_year,\n.rdp-dropdown_month {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n}\n\n.rdp-dropdown {\n  appearance: none;\n  position: absolute;\n  z-index: 2;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  margin: 0;\n  padding: 0;\n  cursor: inherit;\n  opacity: 0;\n  border: none;\n  background-color: transparent;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n\n.rdp-dropdown[disabled] {\n  opacity: unset;\n  color: unset;\n}\n\n.rdp-dropdown:focus-visible:not([disabled]) + .rdp-caption_label {\n  background-color: var(--rdp-background-color);\n  border: var(--rdp-outline);\n  border-radius: 6px;\n}\n\n.rdp-dropdown_icon {\n  margin: 0 0 0 5px;\n}\n\n.rdp-head {\n  border: 0;\n}\n\n.rdp-head_row,\n.rdp-row {\n  height: 100%;\n}\n\n.rdp-head_cell {\n  vertical-align: middle;\n  font-size: 0.75em;\n  font-weight: 700;\n  text-align: center;\n  height: 100%;\n  height: var(--rdp-cell-size);\n  padding: 0;\n  text-transform: uppercase;\n}\n\n.rdp-tbody {\n  border: 0;\n}\n\n.rdp-tfoot {\n  margin: 0.5em;\n}\n\n.rdp-cell {\n  width: var(--rdp-cell-size);\n  height: 100%;\n  height: var(--rdp-cell-size);\n  padding: 0;\n  text-align: center;\n}\n\n.rdp-weeknumber {\n  font-size: 0.75em;\n}\n\n.rdp-weeknumber,\n.rdp-day {\n  display: flex;\n  overflow: hidden;\n  align-items: center;\n  justify-content: center;\n  box-sizing: border-box;\n  width: var(--rdp-cell-size);\n  max-width: var(--rdp-cell-size);\n  height: var(--rdp-cell-size);\n  margin: 0;\n  border: 2px solid transparent;\n  border-radius: 100%;\n}\n\n.rdp-day_today:not(.rdp-day_outside) {\n  font-weight: bold;\n}\n\n.rdp-day_selected,\n.rdp-day_selected:focus-visible,\n.rdp-day_selected:hover {\n  color: var(--rdp-selected-color);\n  opacity: 1;\n  background-color: var(--rdp-accent-color);\n}\n\n.rdp-day_outside {\n  opacity: 0.5;\n}\n\n.rdp-day_selected:focus-visible {\n  /* Since the background is the same use again the outline */\n  outline: var(--rdp-outline);\n  outline-offset: 2px;\n  z-index: 1;\n}\n\n.rdp:not([dir='rtl']) .rdp-day_range_start:not(.rdp-day_range_end) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.rdp:not([dir='rtl']) .rdp-day_range_end:not(.rdp-day_range_start) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.rdp[dir='rtl'] .rdp-day_range_start:not(.rdp-day_range_end) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.rdp[dir='rtl'] .rdp-day_range_end:not(.rdp-day_range_start) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.rdp-day_range_end.rdp-day_range_start {\n  border-radius: 100%;\n}\n\n.rdp-day_range_middle {\n  border-radius: 0;\n}\n"]}