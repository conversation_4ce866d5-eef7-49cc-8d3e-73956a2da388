/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [["circle", { cx: "12.1", cy: "12.1", r: "1", key: "18d7e5" }]];
const Dot = createLucideIcon("dot", __iconNode);

export { __iconNode, Dot as default };
//# sourceMappingURL=dot.js.map
