{"name": "decimal.js-light", "description": "An arbitrary-precision Decimal type for JavaScript.", "version": "2.5.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "repository": {"type": "git", "url": "https://github.com/MikeMcl/decimal.js-light.git"}, "main": "decimal", "module": "decimal.mjs", "browser": "decimal.js", "types": "decimal.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"test": "node ./test/test.js", "build": "uglifyjs decimal.js --source-map doc/decimal.js.map -c -m -o decimal.min.js --preamble \"/* decimal.js-light v2.5.1 https://github.com/MikeMcl/decimal.js-light/LICENCE */\""}}