import{toNestErrors as e,validateFieldsNatively as r}from"@hookform/resolvers";import{appendErrors as t}from"react-hook-form";function s(s,o,a){return void 0===a&&(a={}),function(o,i,n){try{var u=function(){if(f.issues){var s=function(e,r){for(var s=Object.assign([],e),o={};s.length;){var a=e[0];if(a.path){var i=a.path.join(".");if(o[i]||(o[i]={message:a.message,type:""}),r){var n=o[i].types,u=n&&n[""];o[i]=t(i,r,o,"",u?[].concat(u,a.message):a.message)}s.shift()}}return o}(f.issues,!n.shouldUseNativeValidation&&"all"===n.criteriaMode);return{values:{},errors:e(s,n)}}return n.shouldUseNativeValidation&&r({},n),{values:a.raw?Object.assign({},o):f.value,errors:{}}},f=s["~standard"].validate(o),c=function(){if(f instanceof Promise)return Promise.resolve(f).then(function(e){f=e})}();return Promise.resolve(c&&c.then?c.then(u):u())}catch(e){return Promise.reject(e)}}}export{s as typeschemaResolver};
//# sourceMappingURL=typeschema.module.js.map
