{"version": 3, "file": "index.js", "sources": ["../../src/utils.ts", "../../src/equals.ts", "../../src/comparator.ts", "../../src/index.ts"], "sourcesContent": ["import type {\n  AnyEqualityComparator,\n  Cache,\n  CircularState,\n  Dictionary,\n  State,\n  TypeEqualityComparator,\n} from './internalTypes';\n\nconst { getOwnPropertyNames, getOwnPropertySymbols } = Object;\nconst { hasOwnProperty } = Object.prototype;\n\n/**\n * Combine two comparators into a single comparators.\n */\nexport function combineComparators<Meta>(\n  comparatorA: AnyEqualityComparator<Meta>,\n  comparatorB: AnyEqualityComparator<Meta>,\n) {\n  return function isEqual<A, B>(a: A, b: B, state: State<Meta>) {\n    return comparatorA(a, b, state) && comparatorB(a, b, state);\n  };\n}\n\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular state, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nexport function createIsCircular<\n  AreItemsEqual extends TypeEqualityComparator<any, any>,\n>(areItemsEqual: AreItemsEqual): AreItemsEqual {\n  return function isCircular(\n    a: any,\n    b: any,\n    state: CircularState<Cache<any, any>>,\n  ) {\n    if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n      return areItemsEqual(a, b, state);\n    }\n\n    const { cache } = state;\n\n    const cachedA = cache.get(a);\n    const cachedB = cache.get(b);\n\n    if (cachedA && cachedB) {\n      return cachedA === b && cachedB === a;\n    }\n\n    cache.set(a, b);\n    cache.set(b, a);\n\n    const result = areItemsEqual(a, b, state);\n\n    cache.delete(a);\n    cache.delete(b);\n\n    return result;\n  } as AreItemsEqual;\n}\n\n/**\n * Get the properties to strictly examine, which include both own properties that are\n * not enumerable and symbol properties.\n */\nexport function getStrictProperties(\n  object: Dictionary,\n): Array<string | symbol> {\n  return (getOwnPropertyNames(object) as Array<string | symbol>).concat(\n    getOwnPropertySymbols(object),\n  );\n}\n\n/**\n * Whether the object contains the property passed as an own property.\n */\nexport const hasOwn =\n  Object.hasOwn ||\n  ((object: Dictionary, property: number | string | symbol) =>\n    hasOwnProperty.call(object, property));\n\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nexport function sameValueZeroEqual(a: any, b: any): boolean {\n  return a === b || (!a && !b && a !== a && b !== b);\n}\n", "import { getStrictProperties, hasOwn, sameValueZeroEqual } from './utils';\nimport type {\n  Dictionary,\n  PrimitiveWrapper,\n  State,\n  TypedArray,\n} from './internalTypes';\n\nconst PREACT_VNODE = '__v';\nconst PREACT_OWNER = '__o';\nconst REACT_OWNER = '_owner';\n\nconst { getOwnPropertyDescriptor, keys } = Object;\n\n/**\n * Whether the arrays are equal in value.\n */\nexport function areArraysEqual(a: any[], b: any[], state: State<any>) {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  while (index-- > 0) {\n    if (!state.equals(a[index], b[index], index, index, a, b, state)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the dates passed are equal in value.\n */\nexport function areDatesEqual(a: Date, b: Date): boolean {\n  return sameValueZeroEqual(a.getTime(), b.getTime());\n}\n\n/**\n * Whether the errors passed are equal in value.\n */\nexport function areErrorsEqual(a: Error, b: Error): boolean {\n  return (\n    a.name === b.name &&\n    a.message === b.message &&\n    a.cause === b.cause &&\n    a.stack === b.stack\n  );\n}\n\n/**\n * Whether the functions passed are equal in value.\n */\nexport function areFunctionsEqual(\n  a: (...args: any[]) => any,\n  b: (...args: any[]) => any,\n): boolean {\n  return a === b;\n}\n\n/**\n * Whether the `Map`s are equal in value.\n */\nexport function areMapsEqual(\n  a: Map<any, any>,\n  b: Map<any, any>,\n  state: State<any>,\n): boolean {\n  const size = a.size;\n\n  if (size !== b.size) {\n    return false;\n  }\n\n  if (!size) {\n    return true;\n  }\n\n  const matchedIndices: Array<true | undefined> = new Array(size);\n  const aIterable = a.entries();\n\n  let aResult: IteratorResult<[any, any]>;\n  let bResult: IteratorResult<[any, any]>;\n  let index = 0;\n\n  while ((aResult = aIterable.next())) {\n    if (aResult.done) {\n      break;\n    }\n\n    const bIterable = b.entries();\n\n    let hasMatch = false;\n    let matchIndex = 0;\n\n    while ((bResult = bIterable.next())) {\n      if (bResult.done) {\n        break;\n      }\n\n      if (matchedIndices[matchIndex]) {\n        matchIndex++;\n        continue;\n      }\n\n      const aEntry = aResult.value;\n      const bEntry = bResult.value;\n\n      if (\n        state.equals(aEntry[0], bEntry[0], index, matchIndex, a, b, state) &&\n        state.equals(aEntry[1], bEntry[1], aEntry[0], bEntry[0], a, b, state)\n      ) {\n        hasMatch = matchedIndices[matchIndex] = true;\n        break;\n      }\n\n      matchIndex++;\n    }\n\n    if (!hasMatch) {\n      return false;\n    }\n\n    index++;\n  }\n\n  return true;\n}\n\n/**\n * Whether the numbers are equal in value.\n */\nexport const areNumbersEqual = sameValueZeroEqual;\n\n/**\n * Whether the objects are equal in value.\n */\nexport function areObjectsEqual(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n): boolean {\n  const properties = keys(a);\n\n  let index = properties.length;\n\n  if (keys(b).length !== index) {\n    return false;\n  }\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    if (!isPropertyEqual(a, b, state, properties[index]!)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the objects are equal in value with strict property checking.\n */\nexport function areObjectsEqualStrict(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n): boolean {\n  const properties = getStrictProperties(a);\n\n  let index = properties.length;\n\n  if (getStrictProperties(b).length !== index) {\n    return false;\n  }\n\n  let property: string | symbol;\n  let descriptorA: ReturnType<typeof getOwnPropertyDescriptor>;\n  let descriptorB: ReturnType<typeof getOwnPropertyDescriptor>;\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    property = properties[index]!;\n\n    if (!isPropertyEqual(a, b, state, property)) {\n      return false;\n    }\n\n    descriptorA = getOwnPropertyDescriptor(a, property);\n    descriptorB = getOwnPropertyDescriptor(b, property);\n\n    if (\n      (descriptorA || descriptorB) &&\n      (!descriptorA ||\n        !descriptorB ||\n        descriptorA.configurable !== descriptorB.configurable ||\n        descriptorA.enumerable !== descriptorB.enumerable ||\n        descriptorA.writable !== descriptorB.writable)\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the primitive wrappers passed are equal in value.\n */\nexport function arePrimitiveWrappersEqual(\n  a: PrimitiveWrapper,\n  b: PrimitiveWrapper,\n): boolean {\n  return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n\n/**\n * Whether the regexps passed are equal in value.\n */\nexport function areRegExpsEqual(a: RegExp, b: RegExp): boolean {\n  return a.source === b.source && a.flags === b.flags;\n}\n\n/**\n * Whether the `Set`s are equal in value.\n */\nexport function areSetsEqual(\n  a: Set<any>,\n  b: Set<any>,\n  state: State<any>,\n): boolean {\n  const size = a.size;\n\n  if (size !== b.size) {\n    return false;\n  }\n\n  if (!size) {\n    return true;\n  }\n\n  const matchedIndices: Array<true | undefined> = new Array(size);\n  const aIterable = a.values();\n\n  let aResult: IteratorResult<any>;\n  let bResult: IteratorResult<any>;\n\n  while ((aResult = aIterable.next())) {\n    if (aResult.done) {\n      break;\n    }\n\n    const bIterable = b.values();\n\n    let hasMatch = false;\n    let matchIndex = 0;\n\n    while ((bResult = bIterable.next())) {\n      if (bResult.done) {\n        break;\n      }\n\n      if (\n        !matchedIndices[matchIndex] &&\n        state.equals(\n          aResult.value,\n          bResult.value,\n          aResult.value,\n          bResult.value,\n          a,\n          b,\n          state,\n        )\n      ) {\n        hasMatch = matchedIndices[matchIndex] = true;\n        break;\n      }\n\n      matchIndex++;\n    }\n\n    if (!hasMatch) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the TypedArray instances are equal in value.\n */\nexport function areTypedArraysEqual(a: TypedArray, b: TypedArray) {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  while (index-- > 0) {\n    if (a[index] !== b[index]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the URL instances are equal in value.\n */\nexport function areUrlsEqual(a: URL, b: URL): boolean {\n  return (\n    a.hostname === b.hostname &&\n    a.pathname === b.pathname &&\n    a.protocol === b.protocol &&\n    a.port === b.port &&\n    a.hash === b.hash &&\n    a.username === b.username &&\n    a.password === b.password\n  );\n}\n\nfunction isPropertyEqual(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n  property: string | symbol,\n) {\n  if (\n    (property === REACT_OWNER ||\n      property === PREACT_OWNER ||\n      property === PREACT_VNODE) &&\n    (a.$$typeof || b.$$typeof)\n  ) {\n    return true;\n  }\n\n  return (\n    hasOwn(b, property) &&\n    state.equals(a[property], b[property], property, property, a, b, state)\n  );\n}\n", "import {\n  areArraysEqual as areArraysEqualDefault,\n  areDatesEqual as areDatesEqualDefault,\n  areErrorsEqual as areErrorsEqualDefault,\n  areFunctionsEqual as areFunctionsEqualDefault,\n  areMapsEqual as areMapsEqualDefault,\n  areNumbersEqual as areNumbersEqualDefault,\n  areObjectsEqual as areObjectsEqualDefault,\n  areObjectsEqualStrict as areObjectsEqualStrictDefault,\n  arePrimitiveWrappersEqual as arePrimitiveWrappersEqualDefault,\n  areRegExpsEqual as areRegExpsEqualDefault,\n  areSetsEqual as areSetsEqualDefault,\n  areTypedArraysEqual as areTypedArraysEqualDefault,\n  areUrlsEqual as areUrlsEqualDefault,\n} from './equals';\nimport { combineComparators, createIsCircular } from './utils';\nimport type {\n  ComparatorConfig,\n  CreateState,\n  CustomEqualCreatorOptions,\n  EqualityComparator,\n  InternalEqualityComparator,\n  State,\n} from './internalTypes';\n\nconst ARGUMENTS_TAG = '[object Arguments]';\nconst BOOLEAN_TAG = '[object Boolean]';\nconst DATE_TAG = '[object Date]';\nconst ERROR_TAG = '[object Error]';\nconst MAP_TAG = '[object Map]';\nconst NUMBER_TAG = '[object Number]';\nconst OBJECT_TAG = '[object Object]';\nconst REG_EXP_TAG = '[object RegExp]';\nconst SET_TAG = '[object Set]';\nconst STRING_TAG = '[object String]';\nconst URL_TAG = '[object URL]';\n\nconst { isArray } = Array;\nconst isTypedArray =\n  typeof ArrayBuffer === 'function' && ArrayBuffer.isView\n    ? ArrayBuffer.isView\n    : null;\nconst { assign } = Object;\nconst getTag = Object.prototype.toString.call.bind(\n  Object.prototype.toString,\n) as (a: object) => string;\n\ninterface CreateIsEqualOptions<Meta> {\n  circular: boolean;\n  comparator: EqualityComparator<Meta>;\n  createState: CreateState<Meta> | undefined;\n  equals: InternalEqualityComparator<Meta>;\n  strict: boolean;\n}\n\n/**\n * Create a comparator method based on the type-specific equality comparators passed.\n */\nexport function createEqualityComparator<Meta>({\n  areArraysEqual,\n  areDatesEqual,\n  areErrorsEqual,\n  areFunctionsEqual,\n  areMapsEqual,\n  areNumbersEqual,\n  areObjectsEqual,\n  arePrimitiveWrappersEqual,\n  areRegExpsEqual,\n  areSetsEqual,\n  areTypedArraysEqual,\n  areUrlsEqual,\n}: ComparatorConfig<Meta>): EqualityComparator<Meta> {\n  /**\n   * compare the value of the two objects and return true if they are equivalent in values\n   */\n  return function comparator(a: any, b: any, state: State<Meta>): boolean {\n    // If the items are strictly equal, no need to do a value comparison.\n    if (a === b) {\n      return true;\n    }\n\n    // If either of the items are nullish and fail the strictly equal check\n    // above, then they must be unequal.\n    if (a == null || b == null) {\n      return false;\n    }\n\n    const type = typeof a;\n\n    if (type !== typeof b) {\n      return false;\n    }\n\n    if (type !== 'object') {\n      if (type === 'number') {\n        return areNumbersEqual(a, b, state);\n      }\n\n      if (type === 'function') {\n        return areFunctionsEqual(a, b, state);\n      }\n\n      // If a primitive value that is not strictly equal, it must be unequal.\n      return false;\n    }\n\n    const constructor = a.constructor;\n\n    // Checks are listed in order of commonality of use-case:\n    //   1. Common complex object types (plain object, array)\n    //   2. Common data values (date, regexp)\n    //   3. Less-common complex object types (map, set)\n    //   4. Less-common data values (promise, primitive wrappers)\n    // Inherently this is both subjective and assumptive, however\n    // when reviewing comparable libraries in the wild this order\n    // appears to be generally consistent.\n\n    // Constructors should match, otherwise there is potential for false positives\n    // between class and subclass or custom object and POJO.\n    if (constructor !== b.constructor) {\n      return false;\n    }\n\n    // `isPlainObject` only checks against the object's own realm. Cross-realm\n    // comparisons are rare, and will be handled in the ultimate fallback, so\n    // we can avoid capturing the string tag.\n    if (constructor === Object) {\n      return areObjectsEqual(a, b, state);\n    }\n\n    // `isArray()` works on subclasses and is cross-realm, so we can avoid capturing\n    // the string tag or doing an `instanceof` check.\n    if (isArray(a)) {\n      return areArraysEqual(a, b, state);\n    }\n\n    // `isTypedArray()` works on all possible TypedArray classes, so we can avoid\n    // capturing the string tag or comparing against all possible constructors.\n    if (isTypedArray != null && isTypedArray(a)) {\n      return areTypedArraysEqual(a, b, state);\n    }\n\n    // Try to fast-path equality checks for other complex object types in the\n    // same realm to avoid capturing the string tag. Strict equality is used\n    // instead of `instanceof` because it is more performant for the common\n    // use-case. If someone is subclassing a native class, it will be handled\n    // with the string tag comparison.\n\n    if (constructor === Date) {\n      return areDatesEqual(a, b, state);\n    }\n\n    if (constructor === RegExp) {\n      return areRegExpsEqual(a, b, state);\n    }\n\n    if (constructor === Map) {\n      return areMapsEqual(a, b, state);\n    }\n\n    if (constructor === Set) {\n      return areSetsEqual(a, b, state);\n    }\n\n    // Since this is a custom object, capture the string tag to determing its type.\n    // This is reasonably performant in modern environments like v8 and SpiderMonkey.\n    const tag = getTag(a);\n\n    if (tag === DATE_TAG) {\n      return areDatesEqual(a, b, state);\n    }\n\n    // For RegExp, the properties are not enumerable, and therefore will give false positives if\n    // tested like a standard object.\n    if (tag === REG_EXP_TAG) {\n      return areRegExpsEqual(a, b, state);\n    }\n\n    if (tag === MAP_TAG) {\n      return areMapsEqual(a, b, state);\n    }\n\n    if (tag === SET_TAG) {\n      return areSetsEqual(a, b, state);\n    }\n\n    if (tag === OBJECT_TAG) {\n      // The exception for value comparison is custom `Promise`-like class instances. These should\n      // be treated the same as standard `Promise` objects, which means strict equality, and if\n      // it reaches this point then that strict equality comparison has already failed.\n      return (\n        typeof a.then !== 'function' &&\n        typeof b.then !== 'function' &&\n        areObjectsEqual(a, b, state)\n      );\n    }\n\n    // If a URL tag, it should be tested explicitly. Like RegExp, the properties are not\n    // enumerable, and therefore will give false positives if tested like a standard object.\n    if (tag === URL_TAG) {\n      return areUrlsEqual(a, b, state);\n    }\n\n    // If an error tag, it should be tested explicitly. Like RegExp, the properties are not\n    // enumerable, and therefore will give false positives if tested like a standard object.\n    if (tag === ERROR_TAG) {\n      return areErrorsEqual(a, b, state);\n    }\n\n    // If an arguments tag, it should be treated as a standard object.\n    if (tag === ARGUMENTS_TAG) {\n      return areObjectsEqual(a, b, state);\n    }\n\n    // As the penultimate fallback, check if the values passed are primitive wrappers. This\n    // is very rare in modern JS, which is why it is deprioritized compared to all other object\n    // types.\n    if (tag === BOOLEAN_TAG || tag === NUMBER_TAG || tag === STRING_TAG) {\n      return arePrimitiveWrappersEqual(a, b, state);\n    }\n\n    // If not matching any tags that require a specific type of comparison, then we hard-code false because\n    // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n    //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n    //     comparison that can be made.\n    //   - For types that can be introspected, but rarely have requirements to be compared\n    //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n    //     use-cases (may be included in a future release, if requested enough).\n    //   - For types that can be introspected but do not have an objective definition of what\n    //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n    // In all cases, these decisions should be reevaluated based on changes to the language and\n    // common development practices.\n    return false;\n  };\n}\n\n/**\n * Create the configuration object used for building comparators.\n */\nexport function createEqualityComparatorConfig<Meta>({\n  circular,\n  createCustomConfig,\n  strict,\n}: CustomEqualCreatorOptions<Meta>): ComparatorConfig<Meta> {\n  let config = {\n    areArraysEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areArraysEqualDefault,\n    areDatesEqual: areDatesEqualDefault,\n    areErrorsEqual: areErrorsEqualDefault,\n    areFunctionsEqual: areFunctionsEqualDefault,\n    areMapsEqual: strict\n      ? combineComparators(areMapsEqualDefault, areObjectsEqualStrictDefault)\n      : areMapsEqualDefault,\n    areNumbersEqual: areNumbersEqualDefault,\n    areObjectsEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areObjectsEqualDefault,\n    arePrimitiveWrappersEqual: arePrimitiveWrappersEqualDefault,\n    areRegExpsEqual: areRegExpsEqualDefault,\n    areSetsEqual: strict\n      ? combineComparators(areSetsEqualDefault, areObjectsEqualStrictDefault)\n      : areSetsEqualDefault,\n    areTypedArraysEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areTypedArraysEqualDefault,\n    areUrlsEqual: areUrlsEqualDefault,\n  };\n\n  if (createCustomConfig) {\n    config = assign({}, config, createCustomConfig(config));\n  }\n\n  if (circular) {\n    const areArraysEqual = createIsCircular(config.areArraysEqual);\n    const areMapsEqual = createIsCircular(config.areMapsEqual);\n    const areObjectsEqual = createIsCircular(config.areObjectsEqual);\n    const areSetsEqual = createIsCircular(config.areSetsEqual);\n\n    config = assign({}, config, {\n      areArraysEqual,\n      areMapsEqual,\n      areObjectsEqual,\n      areSetsEqual,\n    });\n  }\n\n  return config;\n}\n\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nexport function createInternalEqualityComparator<Meta>(\n  compare: EqualityComparator<Meta>,\n): InternalEqualityComparator<Meta> {\n  return function (\n    a: any,\n    b: any,\n    _indexOrKeyA: any,\n    _indexOrKeyB: any,\n    _parentA: any,\n    _parentB: any,\n    state: State<Meta>,\n  ) {\n    return compare(a, b, state);\n  };\n}\n\n/**\n * Create the `isEqual` function used by the consuming application.\n */\nexport function createIsEqual<Meta>({\n  circular,\n  comparator,\n  createState,\n  equals,\n  strict,\n}: CreateIsEqualOptions<Meta>) {\n  if (createState) {\n    return function isEqual<A, B>(a: A, b: B): boolean {\n      const { cache = circular ? new WeakMap() : undefined, meta } =\n        createState!();\n\n      return comparator(a, b, {\n        cache,\n        equals,\n        meta,\n        strict,\n      } as State<Meta>);\n    };\n  }\n\n  if (circular) {\n    return function isEqual<A, B>(a: A, b: B): boolean {\n      return comparator(a, b, {\n        cache: new WeakMap(),\n        equals,\n        meta: undefined as Meta,\n        strict,\n      } as State<Meta>);\n    };\n  }\n\n  const state = {\n    cache: undefined,\n    equals,\n    meta: undefined,\n    strict,\n  } as State<Meta>;\n\n  return function isEqual<A, B>(a: A, b: B): boolean {\n    return comparator(a, b, state);\n  };\n}\n", "import {\n  createEqualityComparatorConfig,\n  createEqualityComparator,\n  createInternalEqualityComparator,\n  createIsEqual,\n} from './comparator';\nimport type { CustomEqualCreatorOptions } from './internalTypes';\nimport { sameValueZeroEqual } from './utils';\n\nexport { sameValueZeroEqual };\nexport type {\n  AnyEqualityComparator,\n  Cache,\n  CircularState,\n  ComparatorConfig,\n  CreateCustomComparatorConfig,\n  CreateState,\n  CustomEqualCreatorOptions,\n  DefaultState,\n  Dictionary,\n  EqualityComparator,\n  EqualityComparatorCreator,\n  InternalEqualityComparator,\n  PrimitiveWrapper,\n  State,\n  TypeEqualityComparator,\n  TypedArray,\n} from './internalTypes';\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nexport const deepEqual = createCustomEqual();\n\n/**\n * Whether the items passed are deeply-equal in value based on strict comparison.\n */\nexport const strictDeepEqual = createCustomEqual({ strict: true });\n\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nexport const circularDeepEqual = createCustomEqual({ circular: true });\n\n/**\n * Whether the items passed are deeply-equal in value, including circular references,\n * based on strict comparison.\n */\nexport const strictCircularDeepEqual = createCustomEqual({\n  circular: true,\n  strict: true,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nexport const shallowEqual = createCustomEqual({\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value based on strict comparison\n */\nexport const strictShallowEqual = createCustomEqual({\n  strict: true,\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nexport const circularShallowEqual = createCustomEqual({\n  circular: true,\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value, including circular references,\n * based on strict comparison.\n */\nexport const strictCircularShallowEqual = createCustomEqual({\n  circular: true,\n  createInternalComparator: () => sameValueZeroEqual,\n  strict: true,\n});\n\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nexport function createCustomEqual<Meta = undefined>(\n  options: CustomEqualCreatorOptions<Meta> = {},\n) {\n  const {\n    circular = false,\n    createInternalComparator: createCustomInternalComparator,\n    createState,\n    strict = false,\n  } = options;\n\n  const config = createEqualityComparatorConfig<Meta>(options);\n  const comparator = createEqualityComparator(config);\n  const equals = createCustomInternalComparator\n    ? createCustomInternalComparator(comparator)\n    : createInternalEqualityComparator(comparator);\n\n  return createIsEqual({ circular, comparator, createState, equals, strict });\n}\n"], "names": ["areObjectsEqualStrictDefault", "areArraysEqualDefault", "areDatesEqualDefault", "areErrorsEqualDefault", "areFunctionsEqualDefault", "areMapsEqualDefault", "areNumbersEqualDefault", "areObjectsEqualDefault", "arePrimitiveWrappersEqualDefault", "areRegExpsEqualDefault", "areSetsEqualDefault", "areTypedArraysEqualDefault", "areUrlsEqualDefault", "areArraysEqual", "areMapsEqual", "areObjectsEqual", "areSetsEqual"], "mappings": ";;;;;;IASQ,IAAA,mBAAmB,GAA4B,MAAM,CAAA,mBAAlC,EAAE,qBAAqB,GAAK,MAAM,CAAA,qBAAX,CAAY;IACtD,IAAA,cAAc,GAAK,MAAM,CAAC,SAAS,eAArB,CAAsB;IAE5C;;IAEG;IACa,SAAA,kBAAkB,CAChC,WAAwC,EACxC,WAAwC,EAAA;IAExC,IAAA,OAAO,SAAS,OAAO,CAAO,CAAI,EAAE,CAAI,EAAE,KAAkB,EAAA;IAC1D,QAAA,OAAO,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAC9D,KAAC,CAAC;IACJ,CAAC;IAED;;;;IAIG;IACG,SAAU,gBAAgB,CAE9B,aAA4B,EAAA;IAC5B,IAAA,OAAO,SAAS,UAAU,CACxB,CAAM,EACN,CAAM,EACN,KAAqC,EAAA;IAErC,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;gBAC9D,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACnC,SAAA;IAEO,QAAA,IAAA,KAAK,GAAK,KAAK,CAAA,KAAV,CAAW;YAExB,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAE7B,IAAI,OAAO,IAAI,OAAO,EAAE;IACtB,YAAA,OAAO,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;IACvC,SAAA;IAED,QAAA,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,QAAA,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEhB,IAAM,MAAM,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAE1C,QAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAChB,QAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAEhB,QAAA,OAAO,MAAM,CAAC;IAChB,KAAkB,CAAC;IACrB,CAAC;IAED;;;IAGG;IACG,SAAU,mBAAmB,CACjC,MAAkB,EAAA;IAElB,IAAA,OAAQ,mBAAmB,CAAC,MAAM,CAA4B,CAAC,MAAM,CACnE,qBAAqB,CAAC,MAAM,CAAC,CAC9B,CAAC;IACJ,CAAC;IAED;;IAEG;IACI,IAAM,MAAM,GACjB,MAAM,CAAC,MAAM;SACZ,UAAC,MAAkB,EAAE,QAAkC,EAAA;IACtD,QAAA,OAAA,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;IAArC,KAAqC,CAAC,CAAC;IAE3C;;IAEG;IACa,SAAA,kBAAkB,CAAC,CAAM,EAAE,CAAM,EAAA;IAC/C,IAAA,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACrD;;IC/EA,IAAM,YAAY,GAAG,KAAK,CAAC;IAC3B,IAAM,YAAY,GAAG,KAAK,CAAC;IAC3B,IAAM,WAAW,GAAG,QAAQ,CAAC;IAErB,IAAA,wBAAwB,GAAW,MAAM,CAAA,wBAAjB,EAAE,IAAI,GAAK,MAAM,CAAA,IAAX,CAAY;IAElD;;IAEG;aACa,cAAc,CAAC,CAAQ,EAAE,CAAQ,EAAE,KAAiB,EAAA;IAClE,IAAA,IAAI,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;IAErB,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;IACtB,QAAA,OAAO,KAAK,CAAC;IACd,KAAA;IAED,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;YAClB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE;IAChE,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;IACF,KAAA;IAED,IAAA,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;IAEG;IACa,SAAA,aAAa,CAAC,CAAO,EAAE,CAAO,EAAA;IAC5C,IAAA,OAAO,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACtD,CAAC;IAED;;IAEG;IACa,SAAA,cAAc,CAAC,CAAQ,EAAE,CAAQ,EAAA;IAC/C,IAAA,QACE,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;IACjB,QAAA,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,OAAO;IACvB,QAAA,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK;IACnB,QAAA,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,EACnB;IACJ,CAAC;IAED;;IAEG;IACa,SAAA,iBAAiB,CAC/B,CAA0B,EAC1B,CAA0B,EAAA;QAE1B,OAAO,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC;IAED;;IAEG;aACa,YAAY,CAC1B,CAAgB,EAChB,CAAgB,EAChB,KAAiB,EAAA;IAEjB,IAAA,IAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;IAEpB,IAAA,IAAI,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE;IACnB,QAAA,OAAO,KAAK,CAAC;IACd,KAAA;QAED,IAAI,CAAC,IAAI,EAAE;IACT,QAAA,OAAO,IAAI,CAAC;IACb,KAAA;IAED,IAAA,IAAM,cAAc,GAA4B,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;IAChE,IAAA,IAAM,SAAS,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;IAE9B,IAAA,IAAI,OAAmC,CAAC;IACxC,IAAA,IAAI,OAAmC,CAAC;QACxC,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,QAAQ,OAAO,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG;YACnC,IAAI,OAAO,CAAC,IAAI,EAAE;gBAChB,MAAM;IACP,SAAA;IAED,QAAA,IAAM,SAAS,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;YAE9B,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,QAAQ,OAAO,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG;gBACnC,IAAI,OAAO,CAAC,IAAI,EAAE;oBAChB,MAAM;IACP,aAAA;IAED,YAAA,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE;IAC9B,gBAAA,UAAU,EAAE,CAAC;oBACb,SAAS;IACV,aAAA;IAED,YAAA,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,YAAA,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;gBAE7B,IACE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;IAClE,gBAAA,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EACrE;IACA,gBAAA,QAAQ,GAAG,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;oBAC7C,MAAM;IACP,aAAA;IAED,YAAA,UAAU,EAAE,CAAC;IACd,SAAA;YAED,IAAI,CAAC,QAAQ,EAAE;IACb,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;IAED,QAAA,KAAK,EAAE,CAAC;IACT,KAAA;IAED,IAAA,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;IAEG;IACI,IAAM,eAAe,GAAG,kBAAkB,CAAC;IAElD;;IAEG;aACa,eAAe,CAC7B,CAAa,EACb,CAAa,EACb,KAAiB,EAAA;IAEjB,IAAA,IAAM,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAE3B,IAAA,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;QAE9B,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;IAC5B,QAAA,OAAO,KAAK,CAAC;IACd,KAAA;;;;;IAMD,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;IAClB,QAAA,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,CAAE,CAAC,EAAE;IACrD,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;IACF,KAAA;IAED,IAAA,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;IAEG;aACa,qBAAqB,CACnC,CAAa,EACb,CAAa,EACb,KAAiB,EAAA;IAEjB,IAAA,IAAM,UAAU,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;IAE1C,IAAA,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;QAE9B,IAAI,mBAAmB,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;IAC3C,QAAA,OAAO,KAAK,CAAC;IACd,KAAA;IAED,IAAA,IAAI,QAAyB,CAAC;IAC9B,IAAA,IAAI,WAAwD,CAAC;IAC7D,IAAA,IAAI,WAAwD,CAAC;;;;;IAM7D,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;IAClB,QAAA,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAE,CAAC;YAE9B,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE;IAC3C,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;IAED,QAAA,WAAW,GAAG,wBAAwB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IACpD,QAAA,WAAW,GAAG,wBAAwB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IAEpD,QAAA,IACE,CAAC,WAAW,IAAI,WAAW;IAC3B,aAAC,CAAC,WAAW;IACX,gBAAA,CAAC,WAAW;IACZ,gBAAA,WAAW,CAAC,YAAY,KAAK,WAAW,CAAC,YAAY;IACrD,gBAAA,WAAW,CAAC,UAAU,KAAK,WAAW,CAAC,UAAU;IACjD,gBAAA,WAAW,CAAC,QAAQ,KAAK,WAAW,CAAC,QAAQ,CAAC,EAChD;IACA,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;IACF,KAAA;IAED,IAAA,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;IAEG;IACa,SAAA,yBAAyB,CACvC,CAAmB,EACnB,CAAmB,EAAA;IAEnB,IAAA,OAAO,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACtD,CAAC;IAED;;IAEG;IACa,SAAA,eAAe,CAAC,CAAS,EAAE,CAAS,EAAA;IAClD,IAAA,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CAAC;IACtD,CAAC;IAED;;IAEG;aACa,YAAY,CAC1B,CAAW,EACX,CAAW,EACX,KAAiB,EAAA;IAEjB,IAAA,IAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;IAEpB,IAAA,IAAI,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE;IACnB,QAAA,OAAO,KAAK,CAAC;IACd,KAAA;QAED,IAAI,CAAC,IAAI,EAAE;IACT,QAAA,OAAO,IAAI,CAAC;IACb,KAAA;IAED,IAAA,IAAM,cAAc,GAA4B,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;IAChE,IAAA,IAAM,SAAS,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;IAE7B,IAAA,IAAI,OAA4B,CAAC;IACjC,IAAA,IAAI,OAA4B,CAAC;QAEjC,QAAQ,OAAO,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG;YACnC,IAAI,OAAO,CAAC,IAAI,EAAE;gBAChB,MAAM;IACP,SAAA;IAED,QAAA,IAAM,SAAS,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;YAE7B,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,QAAQ,OAAO,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG;gBACnC,IAAI,OAAO,CAAC,IAAI,EAAE;oBAChB,MAAM;IACP,aAAA;IAED,YAAA,IACE,CAAC,cAAc,CAAC,UAAU,CAAC;oBAC3B,KAAK,CAAC,MAAM,CACV,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,KAAK,EACb,CAAC,EACD,CAAC,EACD,KAAK,CACN,EACD;IACA,gBAAA,QAAQ,GAAG,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;oBAC7C,MAAM;IACP,aAAA;IAED,YAAA,UAAU,EAAE,CAAC;IACd,SAAA;YAED,IAAI,CAAC,QAAQ,EAAE;IACb,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;IACF,KAAA;IAED,IAAA,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;IAEG;IACa,SAAA,mBAAmB,CAAC,CAAa,EAAE,CAAa,EAAA;IAC9D,IAAA,IAAI,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;IAErB,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;IACtB,QAAA,OAAO,KAAK,CAAC;IACd,KAAA;IAED,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;YAClB,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;IACzB,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;IACF,KAAA;IAED,IAAA,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;IAEG;IACa,SAAA,YAAY,CAAC,CAAM,EAAE,CAAM,EAAA;IACzC,IAAA,QACE,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ;IACzB,QAAA,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ;IACzB,QAAA,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ;IACzB,QAAA,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;IACjB,QAAA,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;IACjB,QAAA,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ;IACzB,QAAA,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ,EACzB;IACJ,CAAC;IAED,SAAS,eAAe,CACtB,CAAa,EACb,CAAa,EACb,KAAiB,EACjB,QAAyB,EAAA;QAEzB,IACE,CAAC,QAAQ,KAAK,WAAW;IACvB,QAAA,QAAQ,KAAK,YAAY;YACzB,QAAQ,KAAK,YAAY;aAC1B,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,EAC1B;IACA,QAAA,OAAO,IAAI,CAAC;IACb,KAAA;IAED,IAAA,QACE,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC;YACnB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EACvE;IACJ;;ICrUA,IAAM,aAAa,GAAG,oBAAoB,CAAC;IAC3C,IAAM,WAAW,GAAG,kBAAkB,CAAC;IACvC,IAAM,QAAQ,GAAG,eAAe,CAAC;IACjC,IAAM,SAAS,GAAG,gBAAgB,CAAC;IACnC,IAAM,OAAO,GAAG,cAAc,CAAC;IAC/B,IAAM,UAAU,GAAG,iBAAiB,CAAC;IACrC,IAAM,UAAU,GAAG,iBAAiB,CAAC;IACrC,IAAM,WAAW,GAAG,iBAAiB,CAAC;IACtC,IAAM,OAAO,GAAG,cAAc,CAAC;IAC/B,IAAM,UAAU,GAAG,iBAAiB,CAAC;IACrC,IAAM,OAAO,GAAG,cAAc,CAAC;IAEvB,IAAA,OAAO,GAAK,KAAK,CAAA,OAAV,CAAW;IAC1B,IAAM,YAAY,GAChB,OAAO,WAAW,KAAK,UAAU,IAAI,WAAW,CAAC,MAAM;UACnD,WAAW,CAAC,MAAM;UAClB,IAAI,CAAC;IACH,IAAA,MAAM,GAAK,MAAM,CAAA,MAAX,CAAY;IAC1B,IAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAChD,MAAM,CAAC,SAAS,CAAC,QAAQ,CACD,CAAC;IAU3B;;IAEG;IACG,SAAU,wBAAwB,CAAO,EAatB,EAAA;IAZvB,IAAA,IAAA,cAAc,GAAA,EAAA,CAAA,cAAA,EACd,aAAa,GAAA,EAAA,CAAA,aAAA,EACb,cAAc,GAAA,EAAA,CAAA,cAAA,EACd,iBAAiB,GAAA,EAAA,CAAA,iBAAA,EACjB,YAAY,GAAA,EAAA,CAAA,YAAA,EACZ,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,yBAAyB,GAAA,EAAA,CAAA,yBAAA,EACzB,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,YAAY,kBAAA,EACZ,mBAAmB,GAAA,EAAA,CAAA,mBAAA,EACnB,YAAY,GAAA,EAAA,CAAA,YAAA,CAAA;IAEZ;;IAEG;IACH,IAAA,OAAO,SAAS,UAAU,CAAC,CAAM,EAAE,CAAM,EAAE,KAAkB,EAAA;;YAE3D,IAAI,CAAC,KAAK,CAAC,EAAE;IACX,YAAA,OAAO,IAAI,CAAC;IACb,SAAA;;;IAID,QAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;IAC1B,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;IAED,QAAA,IAAM,IAAI,GAAG,OAAO,CAAC,CAAC;IAEtB,QAAA,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE;IACrB,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;YAED,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACrB,IAAI,IAAI,KAAK,QAAQ,EAAE;oBACrB,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACrC,aAAA;gBAED,IAAI,IAAI,KAAK,UAAU,EAAE;oBACvB,OAAO,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACvC,aAAA;;IAGD,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;IAED,QAAA,IAAM,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC;;;;;;;;;;;IAalC,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,WAAW,EAAE;IACjC,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;;;;YAKD,IAAI,WAAW,KAAK,MAAM,EAAE;gBAC1B,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACrC,SAAA;;;IAID,QAAA,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;gBACd,OAAO,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACpC,SAAA;;;YAID,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;gBAC3C,OAAO,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACzC,SAAA;;;;;;YAQD,IAAI,WAAW,KAAK,IAAI,EAAE;gBACxB,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACnC,SAAA;YAED,IAAI,WAAW,KAAK,MAAM,EAAE;gBAC1B,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACrC,SAAA;YAED,IAAI,WAAW,KAAK,GAAG,EAAE;gBACvB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAClC,SAAA;YAED,IAAI,WAAW,KAAK,GAAG,EAAE;gBACvB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAClC,SAAA;;;IAID,QAAA,IAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAEtB,IAAI,GAAG,KAAK,QAAQ,EAAE;gBACpB,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACnC,SAAA;;;YAID,IAAI,GAAG,KAAK,WAAW,EAAE;gBACvB,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACrC,SAAA;YAED,IAAI,GAAG,KAAK,OAAO,EAAE;gBACnB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAClC,SAAA;YAED,IAAI,GAAG,KAAK,OAAO,EAAE;gBACnB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAClC,SAAA;YAED,IAAI,GAAG,KAAK,UAAU,EAAE;;;;IAItB,YAAA,QACE,OAAO,CAAC,CAAC,IAAI,KAAK,UAAU;IAC5B,gBAAA,OAAO,CAAC,CAAC,IAAI,KAAK,UAAU;oBAC5B,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAC5B;IACH,SAAA;;;YAID,IAAI,GAAG,KAAK,OAAO,EAAE;gBACnB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAClC,SAAA;;;YAID,IAAI,GAAG,KAAK,SAAS,EAAE;gBACrB,OAAO,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACpC,SAAA;;YAGD,IAAI,GAAG,KAAK,aAAa,EAAE;gBACzB,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACrC,SAAA;;;;YAKD,IAAI,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,UAAU,IAAI,GAAG,KAAK,UAAU,EAAE;gBACnE,OAAO,yBAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAC/C,SAAA;;;;;;;;;;;;IAaD,QAAA,OAAO,KAAK,CAAC;IACf,KAAC,CAAC;IACJ,CAAC;IAED;;IAEG;IACG,SAAU,8BAA8B,CAAO,EAInB,EAAA;IAHhC,IAAA,IAAA,QAAQ,cAAA,EACR,kBAAkB,GAAA,EAAA,CAAA,kBAAA,EAClB,MAAM,GAAA,EAAA,CAAA,MAAA,CAAA;IAEN,IAAA,IAAI,MAAM,GAAG;IACX,QAAA,cAAc,EAAE,MAAM;IACpB,cAAEA,qBAA4B;IAC9B,cAAEC,cAAqB;IACzB,QAAA,aAAa,EAAEC,aAAoB;IACnC,QAAA,cAAc,EAAEC,cAAqB;IACrC,QAAA,iBAAiB,EAAEC,iBAAwB;IAC3C,QAAA,YAAY,EAAE,MAAM;IAClB,cAAE,kBAAkB,CAACC,YAAmB,EAAEL,qBAA4B,CAAC;IACvE,cAAEK,YAAmB;IACvB,QAAA,eAAe,EAAEC,eAAsB;IACvC,QAAA,eAAe,EAAE,MAAM;IACrB,cAAEN,qBAA4B;IAC9B,cAAEO,eAAsB;IAC1B,QAAA,yBAAyB,EAAEC,yBAAgC;IAC3D,QAAA,eAAe,EAAEC,eAAsB;IACvC,QAAA,YAAY,EAAE,MAAM;IAClB,cAAE,kBAAkB,CAACC,YAAmB,EAAEV,qBAA4B,CAAC;IACvE,cAAEU,YAAmB;IACvB,QAAA,mBAAmB,EAAE,MAAM;IACzB,cAAEV,qBAA4B;IAC9B,cAAEW,mBAA0B;IAC9B,QAAA,YAAY,EAAEC,YAAmB;SAClC,CAAC;IAEF,IAAA,IAAI,kBAAkB,EAAE;IACtB,QAAA,MAAM,GAAG,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;IACzD,KAAA;IAED,IAAA,IAAI,QAAQ,EAAE;YACZ,IAAMC,gBAAc,GAAG,gBAAgB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC/D,IAAMC,cAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC3D,IAAMC,iBAAe,GAAG,gBAAgB,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACjE,IAAMC,cAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAE3D,QAAA,MAAM,GAAG,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE;IAC1B,YAAA,cAAc,EAAAH,gBAAA;IACd,YAAA,YAAY,EAAAC,cAAA;IACZ,YAAA,eAAe,EAAAC,iBAAA;IACf,YAAA,YAAY,EAAAC,cAAA;IACb,SAAA,CAAC,CAAC;IACJ,KAAA;IAED,IAAA,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;IAGG;IACG,SAAU,gCAAgC,CAC9C,OAAiC,EAAA;IAEjC,IAAA,OAAO,UACL,CAAM,EACN,CAAM,EACN,YAAiB,EACjB,YAAiB,EACjB,QAAa,EACb,QAAa,EACb,KAAkB,EAAA;YAElB,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAC9B,KAAC,CAAC;IACJ,CAAC;IAED;;IAEG;IACG,SAAU,aAAa,CAAO,EAMP,EAAA;IAL3B,IAAA,IAAA,QAAQ,GAAA,EAAA,CAAA,QAAA,EACR,UAAU,GAAA,EAAA,CAAA,UAAA,EACV,WAAW,GAAA,EAAA,CAAA,WAAA,EACX,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,MAAM,GAAA,EAAA,CAAA,MAAA,CAAA;IAEN,IAAA,IAAI,WAAW,EAAE;IACf,QAAA,OAAO,SAAS,OAAO,CAAO,CAAI,EAAE,CAAI,EAAA;gBAChC,IAAA,EAAA,GACJ,WAAY,EAAE,EADR,aAA4C,EAA5C,KAAK,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,QAAQ,GAAG,IAAI,OAAO,EAAE,GAAG,SAAS,GAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,IAC1C,CAAC;IAEjB,YAAA,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE;IACtB,gBAAA,KAAK,EAAA,KAAA;IACL,gBAAA,MAAM,EAAA,MAAA;IACN,gBAAA,IAAI,EAAA,IAAA;IACJ,gBAAA,MAAM,EAAA,MAAA;IACQ,aAAA,CAAC,CAAC;IACpB,SAAC,CAAC;IACH,KAAA;IAED,IAAA,IAAI,QAAQ,EAAE;IACZ,QAAA,OAAO,SAAS,OAAO,CAAO,CAAI,EAAE,CAAI,EAAA;IACtC,YAAA,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE;oBACtB,KAAK,EAAE,IAAI,OAAO,EAAE;IACpB,gBAAA,MAAM,EAAA,MAAA;IACN,gBAAA,IAAI,EAAE,SAAiB;IACvB,gBAAA,MAAM,EAAA,MAAA;IACQ,aAAA,CAAC,CAAC;IACpB,SAAC,CAAC;IACH,KAAA;IAED,IAAA,IAAM,KAAK,GAAG;IACZ,QAAA,KAAK,EAAE,SAAS;IAChB,QAAA,MAAM,EAAA,MAAA;IACN,QAAA,IAAI,EAAE,SAAS;IACf,QAAA,MAAM,EAAA,MAAA;SACQ,CAAC;IAEjB,IAAA,OAAO,SAAS,OAAO,CAAO,CAAI,EAAE,CAAI,EAAA;YACtC,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACjC,KAAC,CAAC;IACJ;;ICtUA;;IAEG;AACU,QAAA,SAAS,GAAG,iBAAiB,GAAG;IAE7C;;IAEG;AACI,QAAM,eAAe,GAAG,iBAAiB,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE;IAEnE;;IAEG;AACI,QAAM,iBAAiB,GAAG,iBAAiB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;IAEvE;;;IAGG;AACI,QAAM,uBAAuB,GAAG,iBAAiB,CAAC;IACvD,IAAA,QAAQ,EAAE,IAAI;IACd,IAAA,MAAM,EAAE,IAAI;IACb,CAAA,EAAE;IAEH;;IAEG;AACI,QAAM,YAAY,GAAG,iBAAiB,CAAC;IAC5C,IAAA,wBAAwB,EAAE,YAAA,EAAM,OAAA,kBAAkB,GAAA;IACnD,CAAA,EAAE;IAEH;;IAEG;AACI,QAAM,kBAAkB,GAAG,iBAAiB,CAAC;IAClD,IAAA,MAAM,EAAE,IAAI;IACZ,IAAA,wBAAwB,EAAE,YAAA,EAAM,OAAA,kBAAkB,GAAA;IACnD,CAAA,EAAE;IAEH;;IAEG;AACI,QAAM,oBAAoB,GAAG,iBAAiB,CAAC;IACpD,IAAA,QAAQ,EAAE,IAAI;IACd,IAAA,wBAAwB,EAAE,YAAA,EAAM,OAAA,kBAAkB,GAAA;IACnD,CAAA,EAAE;IAEH;;;IAGG;AACI,QAAM,0BAA0B,GAAG,iBAAiB,CAAC;IAC1D,IAAA,QAAQ,EAAE,IAAI;IACd,IAAA,wBAAwB,EAAE,YAAA,EAAM,OAAA,kBAAkB,GAAA;IAClD,IAAA,MAAM,EAAE,IAAI;IACb,CAAA,EAAE;IAEH;;;;;;;IAOG;IACG,SAAU,iBAAiB,CAC/B,OAA6C,EAAA;IAA7C,IAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAA6C,GAAA,EAAA,CAAA,EAAA;QAG3C,IAAA,EAAA,GAIE,OAAO,CAAA,QAJO,EAAhB,QAAQ,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,KAAK,GAAA,EAAA,EACU,8BAA8B,GAGtD,OAAO,CAAA,wBAH+C,EACxD,WAAW,GAET,OAAO,CAFE,WAAA,EACX,EACE,GAAA,OAAO,CADK,MAAA,EAAd,MAAM,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,KAAK,GAAA,EAAA,CACJ;IAEZ,IAAA,IAAM,MAAM,GAAG,8BAA8B,CAAO,OAAO,CAAC,CAAC;IAC7D,IAAA,IAAM,UAAU,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC;QACpD,IAAM,MAAM,GAAG,8BAA8B;IAC3C,UAAE,8BAA8B,CAAC,UAAU,CAAC;IAC5C,UAAE,gCAAgC,CAAC,UAAU,CAAC,CAAC;IAEjD,IAAA,OAAO,aAAa,CAAC,EAAE,QAAQ,EAAA,QAAA,EAAE,UAAU,EAAA,UAAA,EAAE,WAAW,EAAA,WAAA,EAAE,MAAM,EAAA,MAAA,EAAE,MAAM,EAAA,MAAA,EAAE,CAAC,CAAC;IAC9E;;;;;;;;;;;;;;;;;"}