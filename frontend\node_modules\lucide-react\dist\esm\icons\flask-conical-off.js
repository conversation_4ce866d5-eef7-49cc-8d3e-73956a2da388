/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 2v2.343", key: "15t272" }],
  ["path", { d: "M14 2v6.343", key: "sxr80q" }],
  ["path", { d: "m2 2 20 20", key: "1ooewy" }],
  ["path", { d: "M20 20a2 2 0 0 1-2 2H6a2 2 0 0 1-1.755-2.96l5.227-9.563", key: "k0duyd" }],
  ["path", { d: "M6.453 15H15", key: "1f0z33" }],
  ["path", { d: "M8.5 2h7", key: "csnxdl" }]
];
const FlaskConicalOff = createLucideIcon("flask-conical-off", __iconNode);

export { __iconNode, FlaskConicalOff as default };
//# sourceMappingURL=flask-conical-off.js.map
