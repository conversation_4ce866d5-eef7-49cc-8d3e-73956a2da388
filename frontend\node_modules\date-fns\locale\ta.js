import { formatDistance } from "./ta/_lib/formatDistance.js";
import { formatLong } from "./ta/_lib/formatLong.js";
import { formatRelative } from "./ta/_lib/formatRelative.js";
import { localize } from "./ta/_lib/localize.js";
import { match } from "./ta/_lib/match.js";

/**
 * @category Locales
 * @summary Tamil locale (India).
 * @language Tamil
 * @iso-639-2 tam
 * <AUTHOR> [@sibiraj-s](https://github.com/sibiraj-s)
 */
export const ta = {
  code: "ta",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default ta;
