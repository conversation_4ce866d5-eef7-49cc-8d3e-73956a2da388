/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M7 2h10", key: "nczekb" }],
  ["path", { d: "M5 6h14", key: "u2x4p" }],
  ["rect", { width: "18", height: "12", x: "3", y: "10", rx: "2", key: "l0tzu3" }]
];
const GalleryVerticalEnd = createLucideIcon("gallery-vertical-end", __iconNode);

export { __iconNode, GalleryVerticalEnd as default };
//# sourceMappingURL=gallery-vertical-end.js.map
