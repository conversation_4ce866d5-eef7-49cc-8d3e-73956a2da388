/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M2 8V2h6", key: "hiwtdz" }],
  ["path", { d: "m2 2 10 10", key: "1oh8rs" }],
  ["path", { d: "M12 2A10 10 0 1 1 2 12", key: "rrk4fa" }]
];
const CircleArrowOutUpLeft = createLucideIcon("circle-arrow-out-up-left", __iconNode);

export { __iconNode, CircleArrowOutUpLeft as default };
//# sourceMappingURL=circle-arrow-out-up-left.js.map
