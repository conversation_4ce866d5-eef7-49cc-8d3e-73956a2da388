{
  message: 'Server running on 0.0.0.0:3000',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-13 17:44:17'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-13 17:44:17'
}
{
  message: 'Server running on 0.0.0.0:3000 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-13 17:44:37'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-13 17:44:37'
}
{
  message: 'SIGTERM received, shutting down gracefully',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-13 17:44:47'
}
{
  message: 'Server running on 0.0.0.0:3000 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-13 18:09:45'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-13 18:09:45'
}
{
  message: 'Server running on 0.0.0.0:3000 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-13 18:10:29'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-13 18:10:29'
}
{
  service: 'stresser-ddos-backend',
  method: 'GET',
  url: '/api/docs',
  status: 200,
  duration: '7ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-13 18:12:37'
}
{
  service: 'stresser-ddos-backend',
  method: 'GET',
  url: '/favicon.ico',
  status: 404,
  duration: '1ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'warn',
  message: 'HTTP request completed with error',
  timestamp: '2025-07-13 18:12:37'
}
{
  service: 'stresser-ddos-backend',
  method: 'GET',
  url: '/health',
  status: 200,
  duration: '2ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-13 18:12:43'
}
{
  service: 'stresser-ddos-backend',
  method: 'GET',
  url: '/',
  status: 404,
  duration: '1ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'warn',
  message: 'HTTP request completed with error',
  timestamp: '2025-07-13 18:22:48'
}
{
  message: 'Server running on 0.0.0.0:3000 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:29:33'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:29:33'
}
{
  message: 'Server running on 0.0.0.0:3000 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:31:32'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:31:32'
}
{
  message: 'Server running on 0.0.0.0:3000 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:32:23'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:32:24'
}
{
  message: 'Server running on 0.0.0.0:3000 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:42:58'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:42:58'
}
{
  message: 'Server running on 0.0.0.0:3000 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:43:42'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:43:42'
}
{
  message: 'Server running on 0.0.0.0:3000 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:44:04'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:44:04'
}
{
  message: 'Server running on 0.0.0.0:3000 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:45:17'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 08:45:17'
}
{
  message: 'Server running on 0.0.0.0:3000 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:04:55'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:04:55'
}
{
  service: 'stresser-ddos-backend',
  method: 'GET',
  url: '/',
  status: 404,
  duration: '12ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'warn',
  message: 'HTTP request completed with error',
  timestamp: '2025-07-31 22:05:16'
}
{
  service: 'stresser-ddos-backend',
  method: 'GET',
  url: '/favicon.ico',
  status: 404,
  duration: '1ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'warn',
  message: 'HTTP request completed with error',
  timestamp: '2025-07-31 22:05:17'
}
{
  service: 'stresser-ddos-backend',
  method: 'GET',
  url: '/',
  status: 404,
  duration: '1ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'warn',
  message: 'HTTP request completed with error',
  timestamp: '2025-07-31 22:05:19'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:05:54'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:05:54'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:06:38'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:06:38'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:06:58'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:06:58'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:10:06'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:10:06'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:10:33'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:10:33'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688b025319b7e742b809806c'),
  username: 'admin',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'User logged in: admin',
  timestamp: '2025-07-31 22:10:51'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/login',
  status: 200,
  duration: '892ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:10:51'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688b025319b7e742b809806c'),
  username: 'admin',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'User logged in: admin',
  timestamp: '2025-07-31 22:10:52'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/login',
  status: 200,
  duration: '919ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:10:52'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688b025319b7e742b809806c'),
  username: 'admin',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'User logged in: admin',
  timestamp: '2025-07-31 22:11:08'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/login',
  status: 200,
  duration: '665ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:11:08'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688b025319b7e742b809806c'),
  username: 'admin',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'User logged in: admin',
  timestamp: '2025-07-31 22:11:27'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/login',
  status: 200,
  duration: '694ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:11:27'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688bc0212975b23cb0c33512'),
  username: 'mrsajad',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'New user registered: mrsajad',
  timestamp: '2025-07-31 22:12:34'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/register',
  status: 201,
  duration: '648ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:12:34'
}
{
  service: 'stresser-ddos-backend',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  endpoint: '/api/auth/login',
  level: 'warn',
  message: 'Rate limit exceeded',
  timestamp: '2025-07-31 22:15:57'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/login',
  status: 429,
  duration: '3ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'warn',
  message: 'HTTP request completed with error',
  timestamp: '2025-07-31 22:15:57'
}
{
  message: 'MongoDB disconnected',
  level: 'warn',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:17:36'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:17:47'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:17:47'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688b025319b7e742b809806c'),
  username: 'admin',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'User logged in: admin',
  timestamp: '2025-07-31 22:18:00'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/login',
  status: 200,
  duration: '782ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:18:00'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:19:13'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:19:13'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:19:44'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:19:44'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688bc1d4b979100cbab6dbc6'),
  username: 'qqqq',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'New user registered: qqqq',
  timestamp: '2025-07-31 22:19:49'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/register',
  status: 201,
  duration: '972ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:19:49'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/register',
  status: 400,
  duration: '14ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'warn',
  message: 'HTTP request completed with error',
  timestamp: '2025-07-31 22:21:16'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688bc244b979100cbab6dbca'),
  username: 'qqqqw',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'New user registered: qqqqw',
  timestamp: '2025-07-31 22:21:40'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/register',
  status: 201,
  duration: '642ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:21:40'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688bc288b979100cbab6dbcd'),
  username: 'sssss',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'New user registered: sssss',
  timestamp: '2025-07-31 22:22:48'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/register',
  status: 201,
  duration: '645ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:22:48'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688bc317b979100cbab6dbd0'),
  username: 'characters',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'New user registered: characters',
  timestamp: '2025-07-31 22:25:12'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/register',
  status: 201,
  duration: '612ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:25:12'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688b025319b7e742b809806c'),
  username: 'admin',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'User logged in: admin',
  timestamp: '2025-07-31 22:31:21'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/login',
  status: 200,
  duration: '644ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:31:21'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/register',
  status: 400,
  duration: '3ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'warn',
  message: 'HTTP request completed with error',
  timestamp: '2025-07-31 22:34:50'
}
{
  message: 'MongoDB disconnected',
  level: 'warn',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:35:44'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:35:54'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:35:54'
}
{
  service: 'stresser-ddos-backend',
  errors: {
    password_hash: ValidatorError: Password must be at least 8 characters long
        at validate (C:\Users\<USER>\Desktop\stresser-ddos-platform\backend\node_modules\mongoose\lib\schemaType.js:1407:13)
        at SchemaType.doValidate (C:\Users\<USER>\Desktop\stresser-ddos-platform\backend\node_modules\mongoose\lib\schemaType.js:1391:7)
        at C:\Users\<USER>\Desktop\stresser-ddos-platform\backend\node_modules\mongoose\lib\document.js:3107:18
        at process.processTicksAndRejections (node:internal/process/task_queues:85:11) {
      properties: {
        validator: [Function (anonymous)],
        message: 'Password must be at least 8 characters long',
        type: 'minlength',
        minlength: 8,
        path: 'password_hash',
        fullPath: undefined,
        value: '734568'
      },
      kind: 'minlength',
      path: 'password_hash',
      value: '734568',
      reason: undefined,
      [Symbol(mongoose#validatorError)]: true
    }
  },
  _message: 'User validation failed',
  level: 'error',
  message: 'Registration error: User validation failed: password_hash: Password must be at least 8 characters long',
  stack: 'ValidationError: User validation failed: password_hash: Password must be at least 8 characters long\n' +
    '    at Document.invalidate (C:\\Users\\<USER>\\Desktop\\stresser-ddos-platform\\backend\\node_modules\\mongoose\\lib\\document.js:3354:32)\n' +
    '    at C:\\Users\\<USER>\\Desktop\\stresser-ddos-platform\\backend\\node_modules\\mongoose\\lib\\document.js:3115:17\n' +
    '    at C:\\Users\\<USER>\\Desktop\\stresser-ddos-platform\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)',
  timestamp: '2025-07-31 22:36:03'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/register',
  status: 500,
  duration: '81ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'warn',
  message: 'HTTP request completed with error',
  timestamp: '2025-07-31 22:36:03'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:37:00'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:37:00'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:37:27'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:37:27'
}
{
  service: 'stresser-ddos-backend',
  errors: {
    password_hash: ValidatorError: Password must be at least 8 characters long
        at validate (C:\Users\<USER>\Desktop\stresser-ddos-platform\backend\node_modules\mongoose\lib\schemaType.js:1407:13)
        at SchemaType.doValidate (C:\Users\<USER>\Desktop\stresser-ddos-platform\backend\node_modules\mongoose\lib\schemaType.js:1391:7)
        at C:\Users\<USER>\Desktop\stresser-ddos-platform\backend\node_modules\mongoose\lib\document.js:3107:18
        at process.processTicksAndRejections (node:internal/process/task_queues:85:11) {
      properties: {
        validator: [Function (anonymous)],
        message: 'Password must be at least 8 characters long',
        type: 'minlength',
        minlength: 8,
        path: 'password_hash',
        fullPath: undefined,
        value: '734568'
      },
      kind: 'minlength',
      path: 'password_hash',
      value: '734568',
      reason: undefined,
      [Symbol(mongoose#validatorError)]: true
    }
  },
  _message: 'User validation failed',
  level: 'error',
  message: 'Registration error: User validation failed: password_hash: Password must be at least 8 characters long',
  stack: 'ValidationError: User validation failed: password_hash: Password must be at least 8 characters long\n' +
    '    at Document.invalidate (C:\\Users\\<USER>\\Desktop\\stresser-ddos-platform\\backend\\node_modules\\mongoose\\lib\\document.js:3354:32)\n' +
    '    at C:\\Users\\<USER>\\Desktop\\stresser-ddos-platform\\backend\\node_modules\\mongoose\\lib\\document.js:3115:17\n' +
    '    at C:\\Users\\<USER>\\Desktop\\stresser-ddos-platform\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)',
  timestamp: '2025-07-31 22:37:36'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/register',
  status: 500,
  duration: '80ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'warn',
  message: 'HTTP request completed with error',
  timestamp: '2025-07-31 22:37:36'
}
{
  service: 'stresser-ddos-backend',
  errors: {
    password_hash: ValidatorError: Password must be at least 8 characters long
        at validate (C:\Users\<USER>\Desktop\stresser-ddos-platform\backend\node_modules\mongoose\lib\schemaType.js:1407:13)
        at SchemaType.doValidate (C:\Users\<USER>\Desktop\stresser-ddos-platform\backend\node_modules\mongoose\lib\schemaType.js:1391:7)
        at C:\Users\<USER>\Desktop\stresser-ddos-platform\backend\node_modules\mongoose\lib\document.js:3107:18
        at process.processTicksAndRejections (node:internal/process/task_queues:85:11) {
      properties: {
        validator: [Function (anonymous)],
        message: 'Password must be at least 8 characters long',
        type: 'minlength',
        minlength: 8,
        path: 'password_hash',
        fullPath: undefined,
        value: '734568'
      },
      kind: 'minlength',
      path: 'password_hash',
      value: '734568',
      reason: undefined,
      [Symbol(mongoose#validatorError)]: true
    }
  },
  _message: 'User validation failed',
  level: 'error',
  message: 'Registration error: User validation failed: password_hash: Password must be at least 8 characters long',
  stack: 'ValidationError: User validation failed: password_hash: Password must be at least 8 characters long\n' +
    '    at Document.invalidate (C:\\Users\\<USER>\\Desktop\\stresser-ddos-platform\\backend\\node_modules\\mongoose\\lib\\document.js:3354:32)\n' +
    '    at C:\\Users\\<USER>\\Desktop\\stresser-ddos-platform\\backend\\node_modules\\mongoose\\lib\\document.js:3115:17\n' +
    '    at C:\\Users\\<USER>\\Desktop\\stresser-ddos-platform\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)',
  timestamp: '2025-07-31 22:37:58'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/register',
  status: 500,
  duration: '18ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'warn',
  message: 'HTTP request completed with error',
  timestamp: '2025-07-31 22:37:58'
}
{
  message: 'MongoDB disconnected',
  level: 'warn',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:39:14'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:39:23'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-07-31 22:39:24'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688bc6b6fa8e7d04704da889'),
  username: 'sssm',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'New user registered: sssm',
  timestamp: '2025-07-31 22:40:39'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/register',
  status: 201,
  duration: '769ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:40:39'
}
{
  service: 'stresser-ddos-backend',
  userId: new ObjectId('688b025319b7e742b809806c'),
  username: 'admin',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'User logged in: admin',
  timestamp: '2025-07-31 22:59:41'
}
{
  service: 'stresser-ddos-backend',
  method: 'POST',
  url: '/api/auth/login',
  status: 200,
  duration: '626ms',
  ip: '127.0.0.1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  level: 'info',
  message: 'HTTP request completed',
  timestamp: '2025-07-31 22:59:41'
}
{
  message: 'Server running on 0.0.0.0:3001 in development mode',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-08-01 00:17:12'
}
{
  message: 'MongoDB Connected: localhost',
  level: 'info',
  service: 'stresser-ddos-backend',
  timestamp: '2025-08-01 00:17:12'
}
