(()=>{var A;function U(B){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},U(B)}function K(B,G){var C=Object.keys(B);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(B);G&&(H=H.filter(function(J){return Object.getOwnPropertyDescriptor(B,J).enumerable})),C.push.apply(C,H)}return C}function Q(B){for(var G=1;G<arguments.length;G++){var C=arguments[G]!=null?arguments[G]:{};G%2?K(Object(C),!0).forEach(function(H){x(B,H,C[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(C)):K(Object(C)).forEach(function(H){Object.defineProperty(B,H,Object.getOwnPropertyDescriptor(C,H))})}return B}function x(B,G,C){if(G=N(G),G in B)Object.defineProperty(B,G,{value:C,enumerable:!0,configurable:!0,writable:!0});else B[G]=C;return B}function N(B){var G=z(B,"string");return U(G)=="symbol"?G:String(G)}function z(B,G){if(U(B)!="object"||!B)return B;var C=B[Symbol.toPrimitive];if(C!==void 0){var H=C.call(B,G||"default");if(U(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}var W=Object.defineProperty,GB=function B(G,C){for(var H in C)W(G,H,{get:C[H],enumerable:!0,configurable:!0,set:function J(X){return C[H]=function(){return X}}})},S={lessThanXSeconds:{past:"{{count}} \u10EC\u10D0\u10DB\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10D8 \u10EE\u10DC\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10D0\u10DB\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10D8",future:"{{count}} \u10EC\u10D0\u10DB\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10E8\u10D8"},xSeconds:{past:"{{count}} \u10EC\u10D0\u10DB\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10D0\u10DB\u10D8",future:"{{count}} \u10EC\u10D0\u10DB\u10E8\u10D8"},halfAMinute:{past:"\u10DC\u10D0\u10EE\u10D4\u10D5\u10D0\u10E0\u10D8 \u10EC\u10E3\u10D7\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"\u10DC\u10D0\u10EE\u10D4\u10D5\u10D0\u10E0\u10D8 \u10EC\u10E3\u10D7\u10D8",future:"\u10DC\u10D0\u10EE\u10D4\u10D5\u10D0\u10E0\u10D8 \u10EC\u10E3\u10D7\u10E8\u10D8"},lessThanXMinutes:{past:"{{count}} \u10EC\u10E3\u10D7\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10D8 \u10EE\u10DC\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10E3\u10D7\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10D8",future:"{{count}} \u10EC\u10E3\u10D7\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10E8\u10D8"},xMinutes:{past:"{{count}} \u10EC\u10E3\u10D7\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10E3\u10D7\u10D8",future:"{{count}} \u10EC\u10E3\u10D7\u10E8\u10D8"},aboutXHours:{past:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10E1\u10D0\u10D0\u10D7\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10E1\u10D0\u10D0\u10D7\u10D8",future:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10E1\u10D0\u10D0\u10D7\u10E8\u10D8"},xHours:{past:"{{count}} \u10E1\u10D0\u10D0\u10D7\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10E1\u10D0\u10D0\u10D7\u10D8",future:"{{count}} \u10E1\u10D0\u10D0\u10D7\u10E8\u10D8"},xDays:{past:"{{count}} \u10D3\u10E6\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10D3\u10E6\u10D4",future:"{{count}} \u10D3\u10E6\u10D4\u10E8\u10D8"},aboutXWeeks:{past:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D9\u10D5\u10D8\u10E0\u10D0\u10E1 \u10EC\u10D8\u10DC",present:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D9\u10D5\u10D8\u10E0\u10D0",future:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D9\u10D5\u10D8\u10E0\u10D0\u10E8\u10D8"},xWeeks:{past:"{{count}} \u10D9\u10D5\u10D8\u10E0\u10D0\u10E1 \u10D9\u10D5\u10D8\u10E0\u10D0",present:"{{count}} \u10D9\u10D5\u10D8\u10E0\u10D0",future:"{{count}} \u10D9\u10D5\u10D8\u10E0\u10D0\u10E8\u10D8"},aboutXMonths:{past:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D7\u10D5\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D7\u10D5\u10D4",future:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D7\u10D5\u10D4\u10E8\u10D8"},xMonths:{past:"{{count}} \u10D7\u10D5\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10D7\u10D5\u10D4",future:"{{count}} \u10D7\u10D5\u10D4\u10E8\u10D8"},aboutXYears:{past:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10EC\u10DA\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10EC\u10D4\u10DA\u10D8",future:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10EC\u10D4\u10DA\u10E8\u10D8"},xYears:{past:"{{count}} \u10EC\u10DA\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10D4\u10DA\u10D8",future:"{{count}} \u10EC\u10D4\u10DA\u10E8\u10D8"},overXYears:{past:"{{count}} \u10EC\u10D4\u10DA\u10D6\u10D4 \u10DB\u10D4\u10E2\u10D8 \u10EE\u10DC\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10D4\u10DA\u10D6\u10D4 \u10DB\u10D4\u10E2\u10D8",future:"{{count}} \u10EC\u10D4\u10DA\u10D6\u10D4 \u10DB\u10D4\u10E2\u10D8 \u10EE\u10DC\u10D8\u10E1 \u10E8\u10D4\u10DB\u10D3\u10D4\u10D2"},almostXYears:{past:"\u10D7\u10D8\u10D7\u10E5\u10DB\u10D8\u10E1 {{count}} \u10EC\u10DA\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"\u10D7\u10D8\u10D7\u10E5\u10DB\u10D8\u10E1 {{count}} \u10EC\u10D4\u10DA\u10D8",future:"\u10D7\u10D8\u10D7\u10E5\u10DB\u10D8\u10E1 {{count}} \u10EC\u10D4\u10DA\u10E8\u10D8"}},M=function B(G,C,H){var J,X=S[G];if(typeof X==="string")J=X;else if(H!==null&&H!==void 0&&H.addSuffix&&H.comparison&&H.comparison>0)J=X.future.replace("{{count}}",String(C));else if(H!==null&&H!==void 0&&H.addSuffix)J=X.past.replace("{{count}}",String(C));else J=X.present.replace("{{count}}",String(C));return J};function $(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=G.width?String(G.width):B.defaultWidth,H=B.formats[C]||B.formats[B.defaultWidth];return H}}var D={full:"EEEE, do MMMM, y",long:"do, MMMM, y",medium:"d, MMM, y",short:"dd/MM/yyyy"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} {{time}}'-\u10D6\u10D4'",long:"{{date}} {{time}}'-\u10D6\u10D4'",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:$({formats:D,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"'\u10EC\u10D8\u10DC\u10D0' eeee p'-\u10D6\u10D4'",yesterday:"'\u10D2\u10E3\u10E8\u10D8\u10DC' p'-\u10D6\u10D4'",today:"'\u10D3\u10E6\u10D4\u10E1' p'-\u10D6\u10D4'",tomorrow:"'\u10EE\u10D5\u10D0\u10DA' p'-\u10D6\u10D4'",nextWeek:"'\u10E8\u10D4\u10DB\u10D3\u10D4\u10D2\u10D8' eeee p'-\u10D6\u10D4'",other:"P"},w=function B(G,C,H,J){return j[G]};function I(B){return function(G,C){var H=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",J;if(H==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,Y=C!==null&&C!==void 0&&C.width?String(C.width):X;J=B.formattingValues[Y]||B.formattingValues[X]}else{var Z=B.defaultWidth,q=C!==null&&C!==void 0&&C.width?String(C.width):B.defaultWidth;J=B.values[q]||B.values[Z]}var T=B.argumentCallback?B.argumentCallback(G):G;return J[T]}}var _={narrow:["\u10E9.\u10EC-\u10DB\u10D3\u10D4","\u10E9.\u10EC"],abbreviated:["\u10E9\u10D5.\u10EC-\u10DB\u10D3\u10D4","\u10E9\u10D5.\u10EC"],wide:["\u10E9\u10D5\u10D4\u10DC\u10E1 \u10EC\u10D4\u10DA\u10D7\u10D0\u10E6\u10E0\u10D8\u10EA\u10EE\u10D5\u10D0\u10DB\u10D3\u10D4","\u10E9\u10D5\u10D4\u10DC\u10D8 \u10EC\u10D4\u10DA\u10D7\u10D0\u10E6\u10E0\u10D8\u10EA\u10EE\u10D5\u10D8\u10D7"]},f={narrow:["1","2","3","4"],abbreviated:["1-\u10DA\u10D8 \u10D9\u10D5","2-\u10D4 \u10D9\u10D5","3-\u10D4 \u10D9\u10D5","4-\u10D4 \u10D9\u10D5"],wide:["1-\u10DA\u10D8 \u10D9\u10D5\u10D0\u10E0\u10E2\u10D0\u10DA\u10D8","2-\u10D4 \u10D9\u10D5\u10D0\u10E0\u10E2\u10D0\u10DA\u10D8","3-\u10D4 \u10D9\u10D5\u10D0\u10E0\u10E2\u10D0\u10DA\u10D8","4-\u10D4 \u10D9\u10D5\u10D0\u10E0\u10E2\u10D0\u10DA\u10D8"]},F={narrow:["\u10D8\u10D0","\u10D7\u10D4","\u10DB\u10D0","\u10D0\u10DE","\u10DB\u10E1","\u10D5\u10DC","\u10D5\u10DA","\u10D0\u10D2","\u10E1\u10D4","\u10DD\u10E5","\u10DC\u10DD","\u10D3\u10D4"],abbreviated:["\u10D8\u10D0\u10DC","\u10D7\u10D4\u10D1","\u10DB\u10D0\u10E0","\u10D0\u10DE\u10E0","\u10DB\u10D0\u10D8","\u10D8\u10D5\u10DC","\u10D8\u10D5\u10DA","\u10D0\u10D2\u10D5","\u10E1\u10D4\u10E5","\u10DD\u10E5\u10E2","\u10DC\u10DD\u10D4","\u10D3\u10D4\u10D9"],wide:["\u10D8\u10D0\u10DC\u10D5\u10D0\u10E0\u10D8","\u10D7\u10D4\u10D1\u10D4\u10E0\u10D5\u10D0\u10DA\u10D8","\u10DB\u10D0\u10E0\u10E2\u10D8","\u10D0\u10DE\u10E0\u10D8\u10DA\u10D8","\u10DB\u10D0\u10D8\u10E1\u10D8","\u10D8\u10D5\u10DC\u10D8\u10E1\u10D8","\u10D8\u10D5\u10DA\u10D8\u10E1\u10D8","\u10D0\u10D2\u10D5\u10D8\u10E1\u10E2\u10DD","\u10E1\u10D4\u10E5\u10E2\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8","\u10DD\u10E5\u10E2\u10DD\u10DB\u10D1\u10D4\u10E0\u10D8","\u10DC\u10DD\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8","\u10D3\u10D4\u10D9\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8"]},v={narrow:["\u10D9\u10D5","\u10DD\u10E0","\u10E1\u10D0","\u10DD\u10D7","\u10EE\u10E3","\u10DE\u10D0","\u10E8\u10D0"],short:["\u10D9\u10D5\u10D8","\u10DD\u10E0\u10E8","\u10E1\u10D0\u10DB","\u10DD\u10D7\u10EE","\u10EE\u10E3\u10D7","\u10DE\u10D0\u10E0","\u10E8\u10D0\u10D1"],abbreviated:["\u10D9\u10D5\u10D8","\u10DD\u10E0\u10E8","\u10E1\u10D0\u10DB","\u10DD\u10D7\u10EE","\u10EE\u10E3\u10D7","\u10DE\u10D0\u10E0","\u10E8\u10D0\u10D1"],wide:["\u10D9\u10D5\u10D8\u10E0\u10D0","\u10DD\u10E0\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8","\u10E1\u10D0\u10DB\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8","\u10DD\u10D7\u10EE\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8","\u10EE\u10E3\u10D7\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8","\u10DE\u10D0\u10E0\u10D0\u10E1\u10D9\u10D4\u10D5\u10D8","\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8"]},P={narrow:{am:"a",pm:"p",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D4",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4",morning:"\u10D3\u10D8\u10DA\u10D0",afternoon:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",night:"\u10E6\u10D0\u10DB\u10D4"},abbreviated:{am:"AM",pm:"PM",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D4",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4",morning:"\u10D3\u10D8\u10DA\u10D0",afternoon:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",night:"\u10E6\u10D0\u10DB\u10D4"},wide:{am:"a.m.",pm:"p.m.",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D4",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4",morning:"\u10D3\u10D8\u10DA\u10D0",afternoon:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",night:"\u10E6\u10D0\u10DB\u10D4"}},k={narrow:{am:"a",pm:"p",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D8\u10D7",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D8\u10E1\u10D0\u10E1",morning:"\u10D3\u10D8\u10DA\u10D8\u10D7",afternoon:"\u10DC\u10D0\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4\u10D5\u10E1",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD\u10E1",night:"\u10E6\u10D0\u10DB\u10D8\u10D7"},abbreviated:{am:"AM",pm:"PM",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D8\u10D7",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D8\u10E1\u10D0\u10E1",morning:"\u10D3\u10D8\u10DA\u10D8\u10D7",afternoon:"\u10DC\u10D0\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4\u10D5\u10E1",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD\u10E1",night:"\u10E6\u10D0\u10DB\u10D8\u10D7"},wide:{am:"a.m.",pm:"p.m.",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D8\u10D7",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D8\u10E1\u10D0\u10E1",morning:"\u10D3\u10D8\u10DA\u10D8\u10D7",afternoon:"\u10DC\u10D0\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4\u10D5\u10E1",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD\u10E1",night:"\u10E6\u10D0\u10DB\u10D8\u10D7"}},h=function B(G){var C=Number(G);if(C===1)return C+"-\u10DA\u10D8";return C+"-\u10D4"},b={ordinalNumber:h,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.width,J=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],X=G.match(J);if(!X)return null;var Y=X[0],Z=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(Z)?y(Z,function(E){return E.test(Y)}):m(Z,function(E){return E.test(Y)}),T;T=B.valueCallback?B.valueCallback(q):q,T=C.valueCallback?C.valueCallback(T):T;var CB=G.slice(Y.length);return{value:T,rest:CB}}}function m(B,G){for(var C in B)if(Object.prototype.hasOwnProperty.call(B,C)&&G(B[C]))return C;return}function y(B,G){for(var C=0;C<B.length;C++)if(G(B[C]))return C;return}function c(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.match(B.matchPattern);if(!H)return null;var J=H[0],X=G.match(B.parsePattern);if(!X)return null;var Y=B.valueCallback?B.valueCallback(X[0]):X[0];Y=C.valueCallback?C.valueCallback(Y):Y;var Z=G.slice(J.length);return{value:Y,rest:Z}}}var d=/^(\d+)(-ლი|-ე)?/i,g=/\d+/i,p={narrow:/^(ჩვ?\.წ)/i,abbreviated:/^(ჩვ?\.წ)/i,wide:/^(ჩვენს წელთაღრიცხვამდე|ქრისტეშობამდე|ჩვენი წელთაღრიცხვით|ქრისტეშობიდან)/i},u={any:[/^(ჩვენს წელთაღრიცხვამდე|ქრისტეშობამდე)/i,/^(ჩვენი წელთაღრიცხვით|ქრისტეშობიდან)/i]},l={narrow:/^[1234]/i,abbreviated:/^[1234]-(ლი|ე)? კვ/i,wide:/^[1234]-(ლი|ე)? კვარტალი/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={any:/^(ია|თე|მა|აპ|მს|ვნ|ვლ|აგ|სე|ოქ|ნო|დე)/i},s={any:[/^ია/i,/^თ/i,/^მარ/i,/^აპ/i,/^მაი/i,/^ი?ვნ/i,/^ი?ვლ/i,/^აგ/i,/^ს/i,/^ო/i,/^ნ/i,/^დ/i]},o={narrow:/^(კვ|ორ|სა|ოთ|ხუ|პა|შა)/i,short:/^(კვი|ორშ|სამ|ოთხ|ხუთ|პარ|შაბ)/i,wide:/^(კვირა|ორშაბათი|სამშაბათი|ოთხშაბათი|ხუთშაბათი|პარასკევი|შაბათი)/i},r={any:[/^კვ/i,/^ორ/i,/^სა/i,/^ოთ/i,/^ხუ/i,/^პა/i,/^შა/i]},a={any:/^([ap]\.?\s?m\.?|შუაღ|დილ)/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^შუაღ/i,noon:/^შუადღ/i,morning:/^დილ/i,afternoon:/ნაშუადღევს/i,evening:/საღამო/i,night:/ღამ/i}},t={ordinalNumber:c({matchPattern:d,parsePattern:g,valueCallback:function B(G){return parseInt(G,10)}}),era:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:O({matchPatterns:n,defaultMatchWidth:"any",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},BB={code:"ka",formatDistance:M,formatLong:V,formatRelative:w,localize:b,match:t,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{ka:BB})})})();

//# debugId=608615202676023364756E2164756E21
