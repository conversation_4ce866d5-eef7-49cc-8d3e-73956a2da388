/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M21 12H11", key: "wd7e0v" }],
  ["path", { d: "M21 18H11", key: "4wu86t" }],
  ["path", { d: "M21 6H11", key: "6dy1d6" }],
  ["path", { d: "m7 8-4 4 4 4", key: "o5hrat" }]
];
const IndentDecrease = createLucideIcon("indent-decrease", __iconNode);

export { __iconNode, IndentDecrease as default };
//# sourceMappingURL=indent-decrease.js.map
