(()=>{var K;function I(B){return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},I(B)}function W(B,G){var J=Object.keys(B);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(B);G&&(X=X.filter(function(Z){return Object.getOwnPropertyDescriptor(B,Z).enumerable})),J.push.apply(J,X)}return J}function q(B){for(var G=1;G<arguments.length;G++){var J=arguments[G]!=null?arguments[G]:{};G%2?W(Object(J),!0).forEach(function(X){V(B,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(J)):W(Object(J)).forEach(function(X){Object.defineProperty(B,X,Object.getOwnPropertyDescriptor(J,X))})}return B}function V(B,G,J){if(G=j(G),G in B)Object.defineProperty(B,G,{value:J,enumerable:!0,configurable:!0,writable:!0});else B[G]=J;return B}function j(B){var G=w(B,"string");return I(G)=="symbol"?G:String(G)}function w(B,G){if(I(B)!="object"||!B)return B;var J=B[Symbol.toPrimitive];if(J!==void 0){var X=J.call(B,G||"default");if(I(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}var x=Object.defineProperty,OB=function B(G,J){for(var X in J)x(G,X,{get:J[X],enumerable:!0,configurable:!0,set:function Z(C){return J[X]=function(){return C}}})};function D(B){return B.replace(/sekuntia?/,"sekunnin")}function R(B){return B.replace(/minuuttia?/,"minuutin")}function S(B){return B.replace(/tuntia?/,"tunnin")}function _(B){return B.replace(/päivää?/,"p\xE4iv\xE4n")}function L(B){return B.replace(/(viikko|viikkoa)/,"viikon")}function M(B){return B.replace(/(kuukausi|kuukautta)/,"kuukauden")}function $(B){return B.replace(/(vuosi|vuotta)/,"vuoden")}var F={lessThanXSeconds:{one:"alle sekunti",other:"alle {{count}} sekuntia",futureTense:D},xSeconds:{one:"sekunti",other:"{{count}} sekuntia",futureTense:D},halfAMinute:{one:"puoli minuuttia",other:"puoli minuuttia",futureTense:function B(G){return"puolen minuutin"}},lessThanXMinutes:{one:"alle minuutti",other:"alle {{count}} minuuttia",futureTense:R},xMinutes:{one:"minuutti",other:"{{count}} minuuttia",futureTense:R},aboutXHours:{one:"noin tunti",other:"noin {{count}} tuntia",futureTense:S},xHours:{one:"tunti",other:"{{count}} tuntia",futureTense:S},xDays:{one:"p\xE4iv\xE4",other:"{{count}} p\xE4iv\xE4\xE4",futureTense:_},aboutXWeeks:{one:"noin viikko",other:"noin {{count}} viikkoa",futureTense:L},xWeeks:{one:"viikko",other:"{{count}} viikkoa",futureTense:L},aboutXMonths:{one:"noin kuukausi",other:"noin {{count}} kuukautta",futureTense:M},xMonths:{one:"kuukausi",other:"{{count}} kuukautta",futureTense:M},aboutXYears:{one:"noin vuosi",other:"noin {{count}} vuotta",futureTense:$},xYears:{one:"vuosi",other:"{{count}} vuotta",futureTense:$},overXYears:{one:"yli vuosi",other:"yli {{count}} vuotta",futureTense:$},almostXYears:{one:"l\xE4hes vuosi",other:"l\xE4hes {{count}} vuotta",futureTense:$}},P=function B(G,J,X){var Z=F[G],C=J===1?Z.one:Z.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return Z.futureTense(C)+" kuluttua";else return C+" sitten";return C};function Y(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=G.width?String(G.width):B.defaultWidth,X=B.formats[J]||B.formats[B.defaultWidth];return X}}var v={full:"eeee d. MMMM y",long:"d. MMMM y",medium:"d. MMM y",short:"d.M.y"},k={full:"HH.mm.ss zzzz",long:"HH.mm.ss z",medium:"HH.mm.ss",short:"HH.mm"},b={full:"{{date}} 'klo' {{time}}",long:"{{date}} 'klo' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},f={date:Y({formats:v,defaultWidth:"full"}),time:Y({formats:k,defaultWidth:"full"}),dateTime:Y({formats:b,defaultWidth:"full"})},h={lastWeek:"'viime' eeee 'klo' p",yesterday:"'eilen klo' p",today:"'t\xE4n\xE4\xE4n klo' p",tomorrow:"'huomenna klo' p",nextWeek:"'ensi' eeee 'klo' p",other:"P"},m=function B(G,J,X,Z){return h[G]};function O(B){return function(G,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Z;if(X==="formatting"&&B.formattingValues){var C=B.defaultFormattingWidth||B.defaultWidth,T=J!==null&&J!==void 0&&J.width?String(J.width):C;Z=B.formattingValues[T]||B.formattingValues[C]}else{var U=B.defaultWidth,E=J!==null&&J!==void 0&&J.width?String(J.width):B.defaultWidth;Z=B.values[E]||B.values[U]}var H=B.argumentCallback?B.argumentCallback(G):G;return Z[H]}}var c={narrow:["eaa.","jaa."],abbreviated:["eaa.","jaa."],wide:["ennen ajanlaskun alkua","j\xE4lkeen ajanlaskun alun"]},y={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. kvartaali","2. kvartaali","3. kvartaali","4. kvartaali"]},N={narrow:["T","H","M","H","T","K","H","E","S","L","M","J"],abbreviated:["tammi","helmi","maalis","huhti","touko","kes\xE4","hein\xE4","elo","syys","loka","marras","joulu"],wide:["tammikuu","helmikuu","maaliskuu","huhtikuu","toukokuu","kes\xE4kuu","hein\xE4kuu","elokuu","syyskuu","lokakuu","marraskuu","joulukuu"]},p={narrow:N.narrow,abbreviated:N.abbreviated,wide:["tammikuuta","helmikuuta","maaliskuuta","huhtikuuta","toukokuuta","kes\xE4kuuta","hein\xE4kuuta","elokuuta","syyskuuta","lokakuuta","marraskuuta","joulukuuta"]},A={narrow:["S","M","T","K","T","P","L"],short:["su","ma","ti","ke","to","pe","la"],abbreviated:["sunn.","maan.","tiis.","kesk.","torst.","perj.","la"],wide:["sunnuntai","maanantai","tiistai","keskiviikko","torstai","perjantai","lauantai"]},g={narrow:A.narrow,short:A.short,abbreviated:A.abbreviated,wide:["sunnuntaina","maanantaina","tiistaina","keskiviikkona","torstaina","perjantaina","lauantaina"]},d={narrow:{am:"ap",pm:"ip",midnight:"keskiy\xF6",noon:"keskip\xE4iv\xE4",morning:"ap",afternoon:"ip",evening:"illalla",night:"y\xF6ll\xE4"},abbreviated:{am:"ap",pm:"ip",midnight:"keskiy\xF6",noon:"keskip\xE4iv\xE4",morning:"ap",afternoon:"ip",evening:"illalla",night:"y\xF6ll\xE4"},wide:{am:"ap",pm:"ip",midnight:"keskiy\xF6ll\xE4",noon:"keskip\xE4iv\xE4ll\xE4",morning:"aamup\xE4iv\xE4ll\xE4",afternoon:"iltap\xE4iv\xE4ll\xE4",evening:"illalla",night:"y\xF6ll\xE4"}},l=function B(G,J){var X=Number(G);return X+"."},u={ordinalNumber:l,era:O({values:c,defaultWidth:"wide"}),quarter:O({values:y,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:O({values:N,defaultWidth:"wide",formattingValues:p,defaultFormattingWidth:"wide"}),day:O({values:A,defaultWidth:"wide",formattingValues:g,defaultFormattingWidth:"wide"}),dayPeriod:O({values:d,defaultWidth:"wide"})};function Q(B){return function(G){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Z=X&&B.matchPatterns[X]||B.matchPatterns[B.defaultMatchWidth],C=G.match(Z);if(!C)return null;var T=C[0],U=X&&B.parsePatterns[X]||B.parsePatterns[B.defaultParseWidth],E=Array.isArray(U)?n(U,function(z){return z.test(T)}):i(U,function(z){return z.test(T)}),H;H=B.valueCallback?B.valueCallback(E):E,H=J.valueCallback?J.valueCallback(H):H;var IB=G.slice(T.length);return{value:H,rest:IB}}}function i(B,G){for(var J in B)if(Object.prototype.hasOwnProperty.call(B,J)&&G(B[J]))return J;return}function n(B,G){for(var J=0;J<B.length;J++)if(G(B[J]))return J;return}function s(B){return function(G){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=G.match(B.matchPattern);if(!X)return null;var Z=X[0],C=G.match(B.parsePattern);if(!C)return null;var T=B.valueCallback?B.valueCallback(C[0]):C[0];T=J.valueCallback?J.valueCallback(T):T;var U=G.slice(Z.length);return{value:T,rest:U}}}var o=/^(\d+)(\.)/i,r=/\d+/i,a={narrow:/^(e|j)/i,abbreviated:/^(eaa.|jaa.)/i,wide:/^(ennen ajanlaskun alkua|jälkeen ajanlaskun alun)/i},e={any:[/^e/i,/^j/i]},t={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]\.? kvartaali/i},BB={any:[/1/i,/2/i,/3/i,/4/i]},GB={narrow:/^[thmkeslj]/i,abbreviated:/^(tammi|helmi|maalis|huhti|touko|kesä|heinä|elo|syys|loka|marras|joulu)/i,wide:/^(tammikuu|helmikuu|maaliskuu|huhtikuu|toukokuu|kesäkuu|heinäkuu|elokuu|syyskuu|lokakuu|marraskuu|joulukuu)(ta)?/i},JB={narrow:[/^t/i,/^h/i,/^m/i,/^h/i,/^t/i,/^k/i,/^h/i,/^e/i,/^s/i,/^l/i,/^m/i,/^j/i],any:[/^ta/i,/^hel/i,/^maa/i,/^hu/i,/^to/i,/^k/i,/^hei/i,/^e/i,/^s/i,/^l/i,/^mar/i,/^j/i]},XB={narrow:/^[smtkpl]/i,short:/^(su|ma|ti|ke|to|pe|la)/i,abbreviated:/^(sunn.|maan.|tiis.|kesk.|torst.|perj.|la)/i,wide:/^(sunnuntai|maanantai|tiistai|keskiviikko|torstai|perjantai|lauantai)(na)?/i},ZB={narrow:[/^s/i,/^m/i,/^t/i,/^k/i,/^t/i,/^p/i,/^l/i],any:[/^s/i,/^m/i,/^ti/i,/^k/i,/^to/i,/^p/i,/^l/i]},CB={narrow:/^(ap|ip|keskiyö|keskipäivä|aamupäivällä|iltapäivällä|illalla|yöllä)/i,any:/^(ap|ip|keskiyöllä|keskipäivällä|aamupäivällä|iltapäivällä|illalla|yöllä)/i},TB={any:{am:/^ap/i,pm:/^ip/i,midnight:/^keskiyö/i,noon:/^keskipäivä/i,morning:/aamupäivällä/i,afternoon:/iltapäivällä/i,evening:/illalla/i,night:/yöllä/i}},UB={ordinalNumber:s({matchPattern:o,parsePattern:r,valueCallback:function B(G){return parseInt(G,10)}}),era:Q({matchPatterns:a,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"any"}),quarter:Q({matchPatterns:t,defaultMatchWidth:"wide",parsePatterns:BB,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:Q({matchPatterns:GB,defaultMatchWidth:"wide",parsePatterns:JB,defaultParseWidth:"any"}),day:Q({matchPatterns:XB,defaultMatchWidth:"wide",parsePatterns:ZB,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:CB,defaultMatchWidth:"any",parsePatterns:TB,defaultParseWidth:"any"})},HB={code:"fi",formatDistance:P,formatLong:f,formatRelative:m,localize:u,match:UB,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},(K=window.dateFns)===null||K===void 0?void 0:K.locale),{},{fi:HB})})})();

//# debugId=C44581A512FFD17664756E2164756E21
