import{validateFieldsNatively as r,toNestErrors as e}from"@hookform/resolvers";import o from"ajv";import a from"ajv-errors";import s from"ajv-formats";import{appendErrors as t}from"react-hook-form";var i=function(r,e){for(var o={},a=function(r){"required"===r.keyword&&(r.instancePath+="/"+r.params.missingProperty);var a=r.instancePath.substring(1).replace(/\//g,".");if(o[a]||(o[a]={message:r.message,type:r.keyword}),e){var s=o[a].types,i=s&&s[r.keyword];o[a]=t(a,e,o,r.keyword,i?[].concat(i,r.message||""):r.message)}},s=function(){var e=r[i];"errorMessage"===e.keyword?e.params.errors.forEach(function(r){r.message=e.message,a(r)}):a(e)},i=0;i<r.length;i+=1)s();return o},n=function(t,n,m){return void 0===m&&(m={}),function(c,v,f){try{var l=new o(Object.assign({},{allErrors:!0,validateSchema:!0},n));a(l),s(l);var u=l.compile(Object.assign({$async:m&&"async"===m.mode},t)),d=u(c);return f.shouldUseNativeValidation&&r({},f),Promise.resolve(d?{values:c,errors:{}}:{values:{},errors:e(i(u.errors,!f.shouldUseNativeValidation&&"all"===f.criteriaMode),f)})}catch(r){return Promise.reject(r)}}};export{n as ajvResolver};
//# sourceMappingURL=ajv.module.js.map
