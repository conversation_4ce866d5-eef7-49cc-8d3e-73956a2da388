const mongoose = require('mongoose');

const attackMethodSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Attack method name is required'],
    unique: true,
    trim: true,
    uppercase: true,
    minlength: [3, 'Method name must be at least 3 characters long'],
    maxlength: [30, 'Method name cannot exceed 30 characters'],
    match: [/^[A-Z0-9-_]+$/, 'Method name can only contain uppercase letters, numbers, hyphens, and underscores']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    minlength: [10, 'Description must be at least 10 characters long'],
    maxlength: [200, 'Description cannot exceed 200 characters']
  },
  layer: {
    type: String,
    required: [true, 'Layer is required'],
    enum: ['Layer 4', 'Layer 7'],
    default: 'Layer 4'
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: ['HTTP', 'HTTPS', 'UDP', 'TCP', 'ICMP', 'DNS', 'NTP', 'SSDP'],
    default: 'HTTP'
  },
  command_template: {
    type: String,
    required: [true, 'Command template is required'],
    trim: true,
    maxlength: [500, 'Command template cannot exceed 500 characters']
  },
  api_handler: {
    type: String,
    trim: true,
    maxlength: [100, 'API handler cannot exceed 100 characters'],
    default: null
  },
  required_parameters: [{
    type: String,
    trim: true,
    maxlength: [50, 'Parameter name cannot exceed 50 characters']
  }],
  optional_parameters: [{
    type: String,
    trim: true,
    maxlength: [50, 'Parameter name cannot exceed 50 characters']
  }],
  max_duration: {
    type: Number,
    required: [true, 'Max duration is required'],
    min: [1, 'Max duration must be at least 1 second'],
    max: [7200, 'Max duration cannot exceed 7200 seconds'],
    default: 3600
  },
  min_cooldown: {
    type: Number,
    required: [true, 'Min cooldown is required'],
    min: [0, 'Min cooldown cannot be negative'],
    max: [3600, 'Min cooldown cannot exceed 3600 seconds'],
    default: 60
  },
  is_premium: {
    type: Boolean,
    default: false
  },
  is_active: {
    type: Boolean,
    default: true
  },
  success_rate: {
    type: Number,
    min: [0, 'Success rate cannot be negative'],
    max: [100, 'Success rate cannot exceed 100'],
    default: 85
  },
  usage_stats: {
    total_uses: {
      type: Number,
      default: 0
    },
    successful_uses: {
      type: Number,
      default: 0
    },
    last_used: {
      type: Date,
      default: null
    }
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
attackMethodSchema.index({ name: 1 });
attackMethodSchema.index({ layer: 1 });
attackMethodSchema.index({ category: 1 });
attackMethodSchema.index({ is_active: 1 });
attackMethodSchema.index({ is_premium: 1 });

// Virtual for actual success rate
attackMethodSchema.virtual('actualSuccessRate').get(function() {
  if (this.usage_stats.total_uses === 0) {
    return this.success_rate;
  }
  return Math.round((this.usage_stats.successful_uses / this.usage_stats.total_uses) * 100);
});

// Static method to get active methods
attackMethodSchema.statics.getActiveMethods = function(layer = null, premium = null) {
  const query = { is_active: true };
  if (layer) query.layer = layer;
  if (premium !== null) query.is_premium = premium;
  return this.find(query).sort({ name: 1 });
};

// Static method to get methods by category
attackMethodSchema.statics.getMethodsByCategory = function(category) {
  return this.find({ category, is_active: true }).sort({ name: 1 });
};

// Instance method to update usage stats
attackMethodSchema.methods.updateUsageStats = async function(success = true) {
  this.usage_stats.total_uses += 1;
  if (success) {
    this.usage_stats.successful_uses += 1;
  }
  this.usage_stats.last_used = new Date();
  return this.save();
};

// Instance method to check if method is available for user
attackMethodSchema.methods.isAvailableForUser = function(user) {
  if (!this.is_active) return false;
  if (this.is_premium && !user.subscription_id) return false;
  return true;
};

// Pre-save middleware to set default required parameters
attackMethodSchema.pre('save', function(next) {
  if (this.isNew && this.required_parameters.length === 0) {
    this.required_parameters = ['host', 'port', 'time'];
  }
  next();
});

module.exports = mongoose.model('AttackMethod', attackMethodSchema);
