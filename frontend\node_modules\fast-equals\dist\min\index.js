!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((r="undefined"!=typeof globalThis?globalThis:r||self)["fast-equals"]={})}(this,(function(r){"use strict";var e=Object.getOwnPropertyNames,t=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty;function n(r,e){return function(t,a,n){return r(t,a,n)&&e(t,a,n)}}function u(r){return function(e,t,a){if(!e||!t||"object"!=typeof e||"object"!=typeof t)return r(e,t,a);var n=a.cache,u=n.get(e),o=n.get(t);if(u&&o)return u===t&&o===e;n.set(e,t),n.set(t,e);var c=r(e,t,a);return n.delete(e),n.delete(t),c}}function o(r){return e(r).concat(t(r))}var c=Object.hasOwn||function(r,e){return a.call(r,e)};function i(r,e){return r===e||!r&&!e&&r!=r&&e!=e}var l=Object.getOwnPropertyDescriptor,s=Object.keys;function f(r,e,t){var a=r.length;if(e.length!==a)return!1;for(;a-- >0;)if(!t.equals(r[a],e[a],a,a,r,e,t))return!1;return!0}function p(r,e){return i(r.getTime(),e.getTime())}function q(r,e){return r.name===e.name&&r.message===e.message&&r.cause===e.cause&&r.stack===e.stack}function v(r,e){return r===e}function E(r,e,t){var a=r.size;if(a!==e.size)return!1;if(!a)return!0;for(var n,u,o=new Array(a),c=r.entries(),i=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(u=l.next())&&!u.done;)if(o[f])f++;else{var p=n.value,q=u.value;if(t.equals(p[0],q[0],i,f,r,e,t)&&t.equals(p[1],q[1],p[0],q[0],r,e,t)){s=o[f]=!0;break}f++}if(!s)return!1;i++}return!0}var b=i;function m(r,e,t){var a=s(r),n=a.length;if(s(e).length!==n)return!1;for(;n-- >0;)if(!w(r,e,t,a[n]))return!1;return!0}function y(r,e,t){var a,n,u,c=o(r),i=c.length;if(o(e).length!==i)return!1;for(;i-- >0;){if(!w(r,e,t,a=c[i]))return!1;if(n=l(r,a),u=l(e,a),(n||u)&&(!n||!u||n.configurable!==u.configurable||n.enumerable!==u.enumerable||n.writable!==u.writable))return!1}return!0}function g(r,e){return i(r.valueOf(),e.valueOf())}function h(r,e){return r.source===e.source&&r.flags===e.flags}function j(r,e,t){var a=r.size;if(a!==e.size)return!1;if(!a)return!0;for(var n,u,o=new Array(a),c=r.values();(n=c.next())&&!n.done;){for(var i=e.values(),l=!1,s=0;(u=i.next())&&!u.done;){if(!o[s]&&t.equals(n.value,u.value,n.value,u.value,r,e,t)){l=o[s]=!0;break}s++}if(!l)return!1}return!0}function d(r,e){var t=r.length;if(e.length!==t)return!1;for(;t-- >0;)if(r[t]!==e[t])return!1;return!0}function O(r,e){return r.hostname===e.hostname&&r.pathname===e.pathname&&r.protocol===e.protocol&&r.port===e.port&&r.hash===e.hash&&r.username===e.username&&r.password===e.password}function w(r,e,t,a){return!("_owner"!==a&&"__o"!==a&&"__v"!==a||!r.$$typeof&&!e.$$typeof)||c(e,a)&&t.equals(r[a],e[a],a,a,r,e,t)}var S=Array.isArray,A="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,x=Object.assign,C=Object.prototype.toString.call.bind(Object.prototype.toString);var D=z(),M=z({strict:!0}),k=z({circular:!0}),P=z({circular:!0,strict:!0}),T=z({createInternalComparator:function(){return i}}),I=z({strict:!0,createInternalComparator:function(){return i}}),R=z({circular:!0,createInternalComparator:function(){return i}}),_=z({circular:!0,createInternalComparator:function(){return i},strict:!0});function z(r){void 0===r&&(r={});var e,t=r.circular,a=void 0!==t&&t,o=r.createInternalComparator,c=r.createState,i=r.strict,l=void 0!==i&&i,s=function(r){var e=r.circular,t=r.createCustomConfig,a=r.strict,o={areArraysEqual:a?y:f,areDatesEqual:p,areErrorsEqual:q,areFunctionsEqual:v,areMapsEqual:a?n(E,y):E,areNumbersEqual:b,areObjectsEqual:a?y:m,arePrimitiveWrappersEqual:g,areRegExpsEqual:h,areSetsEqual:a?n(j,y):j,areTypedArraysEqual:a?y:d,areUrlsEqual:O};if(t&&(o=x({},o,t(o))),e){var c=u(o.areArraysEqual),i=u(o.areMapsEqual),l=u(o.areObjectsEqual),s=u(o.areSetsEqual);o=x({},o,{areArraysEqual:c,areMapsEqual:i,areObjectsEqual:l,areSetsEqual:s})}return o}(r),w=function(r){var e=r.areArraysEqual,t=r.areDatesEqual,a=r.areErrorsEqual,n=r.areFunctionsEqual,u=r.areMapsEqual,o=r.areNumbersEqual,c=r.areObjectsEqual,i=r.arePrimitiveWrappersEqual,l=r.areRegExpsEqual,s=r.areSetsEqual,f=r.areTypedArraysEqual,p=r.areUrlsEqual;return function(r,q,v){if(r===q)return!0;if(null==r||null==q)return!1;var E=typeof r;if(E!==typeof q)return!1;if("object"!==E)return"number"===E?o(r,q,v):"function"===E&&n(r,q,v);var b=r.constructor;if(b!==q.constructor)return!1;if(b===Object)return c(r,q,v);if(S(r))return e(r,q,v);if(null!=A&&A(r))return f(r,q,v);if(b===Date)return t(r,q,v);if(b===RegExp)return l(r,q,v);if(b===Map)return u(r,q,v);if(b===Set)return s(r,q,v);var m=C(r);return"[object Date]"===m?t(r,q,v):"[object RegExp]"===m?l(r,q,v):"[object Map]"===m?u(r,q,v):"[object Set]"===m?s(r,q,v):"[object Object]"===m?"function"!=typeof r.then&&"function"!=typeof q.then&&c(r,q,v):"[object URL]"===m?p(r,q,v):"[object Error]"===m?a(r,q,v):"[object Arguments]"===m?c(r,q,v):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&i(r,q,v)}}(s);return function(r){var e=r.circular,t=r.comparator,a=r.createState,n=r.equals,u=r.strict;if(a)return function(r,o){var c=a(),i=c.cache,l=void 0===i?e?new WeakMap:void 0:i,s=c.meta;return t(r,o,{cache:l,equals:n,meta:s,strict:u})};if(e)return function(r,e){return t(r,e,{cache:new WeakMap,equals:n,meta:void 0,strict:u})};var o={cache:void 0,equals:n,meta:void 0,strict:u};return function(r,e){return t(r,e,o)}}({circular:a,comparator:w,createState:c,equals:o?o(w):(e=w,function(r,t,a,n,u,o,c){return e(r,t,c)}),strict:l})}r.circularDeepEqual=k,r.circularShallowEqual=R,r.createCustomEqual=z,r.deepEqual=D,r.sameValueZeroEqual=i,r.shallowEqual=T,r.strictCircularDeepEqual=P,r.strictCircularShallowEqual=_,r.strictDeepEqual=M,r.strictShallowEqual=I}));
