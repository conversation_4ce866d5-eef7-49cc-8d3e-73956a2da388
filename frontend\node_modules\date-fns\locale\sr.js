import { formatDistance } from "./sr/_lib/formatDistance.js";
import { formatLong } from "./sr/_lib/formatLong.js";
import { formatRelative } from "./sr/_lib/formatRelative.js";
import { localize } from "./sr/_lib/localize.js";
import { match } from "./sr/_lib/match.js";

/**
 * @category Locales
 * @summary Serbian cyrillic locale.
 * @language Serbian
 * @iso-639-2 srp
 * <AUTHOR> [@rogyvoje](https://github.com/rogyvoje)
 */
export const sr = {
  code: "sr",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default sr;
