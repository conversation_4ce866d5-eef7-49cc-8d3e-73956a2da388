const mongoose = require('mongoose');
const AttackMethod = require('../models/AttackMethod');
const User = require('../models/User');
const { USER_ROLES } = require('../utils/constants');

const attackMethodsData = [
  {
    name: 'HTTP-BYPASS',
    description: 'HTTP Layer 7 bypass method for web applications',
    layer: 'Layer 7',
    category: 'HTTP',
    command_template: 'http_bypass --target {host} --port {port} --time {time}',
    api_handler: 'httpBypassHandler',
    required_parameters: ['host', 'port', 'time'],
    optional_parameters: ['user_agent', 'custom_headers'],
    max_duration: 3600,
    min_cooldown: 60,
    is_premium: false,
    is_active: true,
    success_rate: 85
  },
  {
    name: 'HTTPS-FLOOD',
    description: 'HTTPS flooding attack for secure web applications',
    layer: 'Layer 7',
    category: 'HTTPS',
    command_template: 'https_flood --target {host} --port {port} --time {time}',
    api_handler: 'httpsFloodHandler',
    required_parameters: ['host', 'port', 'time'],
    optional_parameters: ['threads', 'requests_per_second'],
    max_duration: 3600,
    min_cooldown: 90,
    is_premium: true,
    is_active: true,
    success_rate: 90
  },
  {
    name: 'UDP-FLOOD',
    description: 'UDP Layer 4 flooding attack',
    layer: 'Layer 4',
    category: 'UDP',
    command_template: 'udp_flood --target {host} --port {port} --time {time}',
    api_handler: 'udpFloodHandler',
    required_parameters: ['host', 'port', 'time'],
    optional_parameters: ['packet_size', 'threads'],
    max_duration: 7200,
    min_cooldown: 30,
    is_premium: false,
    is_active: true,
    success_rate: 80
  },
  {
    name: 'TCP-SYN',
    description: 'TCP SYN flood attack',
    layer: 'Layer 4',
    category: 'TCP',
    command_template: 'tcp_syn --target {host} --port {port} --time {time}',
    api_handler: 'tcpSynHandler',
    required_parameters: ['host', 'port', 'time'],
    optional_parameters: ['source_ip', 'window_size'],
    max_duration: 7200,
    min_cooldown: 45,
    is_premium: false,
    is_active: true,
    success_rate: 75
  },
  {
    name: 'ICMP-FLOOD',
    description: 'ICMP flooding attack',
    layer: 'Layer 4',
    category: 'ICMP',
    command_template: 'icmp_flood --target {host} --time {time}',
    api_handler: 'icmpFloodHandler',
    required_parameters: ['host', 'time'],
    optional_parameters: ['packet_size', 'interval'],
    max_duration: 3600,
    min_cooldown: 60,
    is_premium: false,
    is_active: true,
    success_rate: 70
  },
  {
    name: 'DNS-AMP',
    description: 'DNS amplification attack',
    layer: 'Layer 4',
    category: 'DNS',
    command_template: 'dns_amp --target {host} --time {time}',
    api_handler: 'dnsAmpHandler',
    required_parameters: ['host', 'time'],
    optional_parameters: ['dns_servers', 'query_type'],
    max_duration: 3600,
    min_cooldown: 120,
    is_premium: true,
    is_active: true,
    success_rate: 95
  }
];

const seedAttackMethods = async () => {
  try {
    // Connect to database if not connected
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/stresser_ddos_platform');
    }

    // Find or create admin user
    let adminUser = await User.findOne({ role: USER_ROLES.ADMIN });
    if (!adminUser) {
      console.log('No admin user found. Creating default admin user...');
      adminUser = new User({
        username: 'admin',
        password_hash: 'admin123', // This will be hashed by the pre-save middleware
        role: USER_ROLES.ADMIN,
        is_active: true,
        is_verified: true
      });
      await adminUser.save();
      console.log('Default admin user created');
    }

    // Clear existing attack methods
    await AttackMethod.deleteMany({});
    console.log('Cleared existing attack methods');

    // Insert new attack methods
    const methodsWithCreator = attackMethodsData.map(method => ({
      ...method,
      created_by: adminUser._id
    }));

    await AttackMethod.insertMany(methodsWithCreator);
    console.log(`Successfully seeded ${attackMethodsData.length} attack methods`);

    // Display created methods
    const createdMethods = await AttackMethod.find().select('name layer category is_premium is_active');
    console.log('\nCreated attack methods:');
    createdMethods.forEach(method => {
      console.log(`- ${method.name} (${method.layer}, ${method.category}) - ${method.is_active ? 'Active' : 'Inactive'} ${method.is_premium ? '(Premium)' : ''}`);
    });

  } catch (error) {
    console.error('Error seeding attack methods:', error);
    throw error;
  }
};

// Run seeder if called directly
if (require.main === module) {
  seedAttackMethods()
    .then(() => {
      console.log('Attack methods seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Attack methods seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedAttackMethods, attackMethodsData };
