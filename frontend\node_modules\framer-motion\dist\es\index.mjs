"use client";
export { AnimatePresence } from './components/AnimatePresence/index.mjs';
export { LayoutGroup } from './components/LayoutGroup/index.mjs';
export { LazyMotion } from './components/LazyMotion/index.mjs';
export { MotionConfig } from './components/MotionConfig/index.mjs';
export { m } from './render/components/m/proxy.mjs';
export { motion } from './render/components/motion/proxy.mjs';
export { addPointerEvent } from './events/add-pointer-event.mjs';
export { addPointerInfo } from './events/event-info.mjs';
export { animations } from './motion/features/animations.mjs';
export { makeUseVisualState } from './motion/utils/use-visual-state.mjs';
export { calcLength } from './projection/geometry/delta-calc.mjs';
export { createBox } from './projection/geometry/models.mjs';
export { filterProps } from './render/dom/utils/filter-props.mjs';
export { isBrowser } from './utils/is-browser.mjs';
export { useForceUpdate } from './utils/use-force-update.mjs';
export { useIsomorphicLayoutEffect } from './utils/use-isomorphic-effect.mjs';
export { useUnmountEffect } from './utils/use-unmount-effect.mjs';
export { domAnimation } from './render/dom/features-animation.mjs';
export { domMax } from './render/dom/features-max.mjs';
export { domMin } from './render/dom/features-min.mjs';
export { useMotionValueEvent } from './utils/use-motion-value-event.mjs';
export { useElementScroll } from './value/scroll/use-element-scroll.mjs';
export { useViewportScroll } from './value/scroll/use-viewport-scroll.mjs';
export { useMotionTemplate } from './value/use-motion-template.mjs';
export { useMotionValue } from './value/use-motion-value.mjs';
export { useScroll } from './value/use-scroll.mjs';
export { useSpring } from './value/use-spring.mjs';
export { useTime } from './value/use-time.mjs';
export { useTransform } from './value/use-transform.mjs';
export { useVelocity } from './value/use-velocity.mjs';
export { useWillChange } from './value/use-will-change/index.mjs';
export { WillChangeMotionValue } from './value/use-will-change/WillChangeMotionValue.mjs';
export { resolveMotionValue } from './value/utils/resolve-motion-value.mjs';
export { useReducedMotion } from './utils/reduced-motion/use-reduced-motion.mjs';
export { useReducedMotionConfig } from './utils/reduced-motion/use-reduced-motion-config.mjs';
export * from 'motion-utils';
export { MotionGlobalConfig } from 'motion-utils';
export { animationControls } from './animation/hooks/animation-controls.mjs';
export { useAnimate } from './animation/hooks/use-animate.mjs';
export { useAnimateMini } from './animation/hooks/use-animate-style.mjs';
export { useAnimation, useAnimationControls } from './animation/hooks/use-animation.mjs';
export { animateVisualElement } from './animation/interfaces/visual-element.mjs';
export { useIsPresent, usePresence } from './components/AnimatePresence/use-presence.mjs';
export { usePresenceData } from './components/AnimatePresence/use-presence-data.mjs';
export { useDomEvent } from './events/use-dom-event.mjs';
export { DragControls, useDragControls } from './gestures/drag/use-drag-controls.mjs';
export { isMotionComponent } from './motion/utils/is-motion-component.mjs';
export { unwrapMotionComponent } from './motion/utils/unwrap-motion-component.mjs';
export { isValidMotionProp } from './motion/utils/valid-prop.mjs';
export { addScaleCorrector } from './projection/styles/scale-correction.mjs';
export { useInstantLayoutTransition } from './projection/use-instant-layout-transition.mjs';
export { useResetProjection } from './projection/use-reset-projection.mjs';
export { buildTransform } from './render/html/utils/build-transform.mjs';
export { visualElementStore } from './render/store.mjs';
export { VisualElement } from './render/VisualElement.mjs';
export { useAnimationFrame } from './utils/use-animation-frame.mjs';
export { useCycle } from './utils/use-cycle.mjs';
export { useInView } from './utils/use-in-view.mjs';
export { disableInstantTransitions, useInstantTransition } from './utils/use-instant-transition.mjs';
export { usePageInView } from './utils/use-page-in-view.mjs';
export { optimizedAppearDataAttribute } from './animation/optimized-appear/data-id.mjs';
export { startOptimizedAppearAnimation } from './animation/optimized-appear/start.mjs';
export { LayoutGroupContext } from './context/LayoutGroupContext.mjs';
export { MotionConfigContext } from './context/MotionConfigContext.mjs';
export { MotionContext } from './context/MotionContext/index.mjs';
export { PresenceContext } from './context/PresenceContext.mjs';
export { SwitchLayoutGroupContext } from './context/SwitchLayoutGroupContext.mjs';
export { FlatTree } from './render/utils/flat-tree.mjs';
export { useAnimatedState as useDeprecatedAnimatedState } from './animation/hooks/use-animated-state.mjs';
export { AnimateSharedLayout } from './components/AnimateSharedLayout.mjs';
export { DeprecatedLayoutGroupContext } from './context/DeprecatedLayoutGroupContext.mjs';
export { useInvertedScale as useDeprecatedInvertedScale } from './value/use-inverted-scale.mjs';
export { delay } from './utils/delay.mjs';
import * as namespace from './components/Reorder/namespace.mjs';
export { namespace as Reorder };
export { animate, createScopedAnimate } from './animation/animate/index.mjs';
export { animateMini } from './animation/animators/waapi/animate-style.mjs';
export { scroll } from './render/dom/scroll/index.mjs';
export { scrollInfo } from './render/dom/scroll/track.mjs';
export { inView } from './render/dom/viewport/index.mjs';
export { distance, distance2D } from './utils/distance.mjs';
export * from 'motion-dom';
