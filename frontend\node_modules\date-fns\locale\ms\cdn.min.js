(()=>{var $;function B(G){return B=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},B(G)}function A(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function Q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?A(Object(J),!0).forEach(function(X){E(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):A(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function E(G,H,J){if(H=N(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function N(G){var H=z(G,"string");return B(H)=="symbol"?H:String(H)}function z(G,H){if(B(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(B(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,JG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},D={lessThanXSeconds:{one:"kurang dari 1 saat",other:"kurang dari {{count}} saat"},xSeconds:{one:"1 saat",other:"{{count}} saat"},halfAMinute:"setengah minit",lessThanXMinutes:{one:"kurang dari 1 minit",other:"kurang dari {{count}} minit"},xMinutes:{one:"1 minit",other:"{{count}} minit"},aboutXHours:{one:"sekitar 1 jam",other:"sekitar {{count}} jam"},xHours:{one:"1 jam",other:"{{count}} jam"},xDays:{one:"1 hari",other:"{{count}} hari"},aboutXWeeks:{one:"sekitar 1 minggu",other:"sekitar {{count}} minggu"},xWeeks:{one:"1 minggu",other:"{{count}} minggu"},aboutXMonths:{one:"sekitar 1 bulan",other:"sekitar {{count}} bulan"},xMonths:{one:"1 bulan",other:"{{count}} bulan"},aboutXYears:{one:"sekitar 1 tahun",other:"sekitar {{count}} tahun"},xYears:{one:"1 tahun",other:"{{count}} tahun"},overXYears:{one:"lebih dari 1 tahun",other:"lebih dari {{count}} tahun"},almostXYears:{one:"hampir 1 tahun",other:"hampir {{count}} tahun"}},S=function G(H,J,X){var Y,Z=D[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"dalam masa "+Y;else return Y+" yang lalu";return Y};function K(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var M={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"d/M/yyyy"},R={full:"HH.mm.ss",long:"HH.mm.ss",medium:"HH.mm",short:"HH.mm"},L={full:"{{date}} 'pukul' {{time}}",long:"{{date}} 'pukul' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:K({formats:M,defaultWidth:"full"}),time:K({formats:R,defaultWidth:"full"}),dateTime:K({formats:L,defaultWidth:"full"})},j={lastWeek:"eeee 'lepas pada jam' p",yesterday:"'Semalam pada jam' p",today:"'Hari ini pada jam' p",tomorrow:"'Esok pada jam' p",nextWeek:"eeee 'pada jam' p",other:"P"},w=function G(H,J,X,Y){return j[H]};function I(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,C=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[C]||G.formattingValues[Z]}else{var T=G.defaultWidth,q=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[q]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(H):H;return Y[U]}}var _={narrow:["SM","M"],abbreviated:["SM","M"],wide:["Sebelum Masihi","Masihi"]},f={narrow:["1","2","3","4"],abbreviated:["S1","S2","S3","S4"],wide:["Suku pertama","Suku kedua","Suku ketiga","Suku keempat"]},F={narrow:["J","F","M","A","M","J","J","O","S","O","N","D"],abbreviated:["Jan","Feb","Mac","Apr","Mei","Jun","Jul","Ogo","Sep","Okt","Nov","Dis"],wide:["Januari","Februari","Mac","April","Mei","Jun","Julai","Ogos","September","Oktober","November","Disember"]},v={narrow:["A","I","S","R","K","J","S"],short:["Ahd","Isn","Sel","Rab","Kha","Jum","Sab"],abbreviated:["Ahd","Isn","Sel","Rab","Kha","Jum","Sab"],wide:["Ahad","Isnin","Selasa","Rabu","Khamis","Jumaat","Sabtu"]},P={narrow:{am:"am",pm:"pm",midnight:"tgh malam",noon:"tgh hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"},abbreviated:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"},wide:{am:"a.m.",pm:"p.m.",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"}},k={narrow:{am:"am",pm:"pm",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"},abbreviated:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"},wide:{am:"a.m.",pm:"p.m.",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"}},b=function G(H,J){return"ke-"+Number(H)},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var C=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(T)?c(T,function(x){return x.test(C)}):m(T,function(x){return x.test(C)}),U;U=G.valueCallback?G.valueCallback(q):q,U=J.valueCallback?J.valueCallback(U):U;var HG=H.slice(C.length);return{value:U,rest:HG}}}function m(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function c(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function y(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var C=G.valueCallback?G.valueCallback(Z[0]):Z[0];C=J.valueCallback?J.valueCallback(C):C;var T=H.slice(Y.length);return{value:C,rest:T}}}var p=/^ke-(\d+)?/i,d=/petama|\d+/i,g={narrow:/^(sm|m)/i,abbreviated:/^(s\.?\s?m\.?|m\.?)/i,wide:/^(sebelum masihi|masihi)/i},u={any:[/^s/i,/^(m)/i]},l={narrow:/^[1234]/i,abbreviated:/^S[1234]/i,wide:/Suku (pertama|kedua|ketiga|keempat)/i},i={any:[/pertama|1/i,/kedua|2/i,/ketiga|3/i,/keempat|4/i]},n={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mac|apr|mei|jun|jul|ogo|sep|okt|nov|dis)/i,wide:/^(januari|februari|mac|april|mei|jun|julai|ogos|september|oktober|november|disember)/i},s={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^o/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^ma/i,/^ap/i,/^me/i,/^jun/i,/^jul/i,/^og/i,/^s/i,/^ok/i,/^n/i,/^d/i]},o={narrow:/^[aisrkj]/i,short:/^(ahd|isn|sel|rab|kha|jum|sab)/i,abbreviated:/^(ahd|isn|sel|rab|kha|jum|sab)/i,wide:/^(ahad|isnin|selasa|rabu|khamis|jumaat|sabtu)/i},r={narrow:[/^a/i,/^i/i,/^s/i,/^r/i,/^k/i,/^j/i,/^s/i],any:[/^a/i,/^i/i,/^se/i,/^r/i,/^k/i,/^j/i,/^sa/i]},a={narrow:/^(am|pm|tengah malam|tengah hari|pagi|petang|malam)/i,any:/^([ap]\.?\s?m\.?|tengah malam|tengah hari|pagi|petang|malam)/i},e={any:{am:/^a/i,pm:/^pm/i,midnight:/^tengah m/i,noon:/^tengah h/i,morning:/pa/i,afternoon:/tengah h/i,evening:/pe/i,night:/m/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function G(H){return parseInt(H,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},GG={code:"ms",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{ms:GG})})})();

//# debugId=384B72356AE8DBD664756E2164756E21
