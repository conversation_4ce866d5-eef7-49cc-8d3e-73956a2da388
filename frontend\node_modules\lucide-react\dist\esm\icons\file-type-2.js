/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4", key: "1pf5j1" }],
  ["path", { d: "M14 2v4a2 2 0 0 0 2 2h4", key: "tnqrlb" }],
  ["path", { d: "M2 13v-1h6v1", key: "1dh9dg" }],
  ["path", { d: "M5 12v6", key: "150t9c" }],
  ["path", { d: "M4 18h2", key: "1xrofg" }]
];
const FileType2 = createLucideIcon("file-type-2", __iconNode);

export { __iconNode, FileType2 as default };
//# sourceMappingURL=file-type-2.js.map
