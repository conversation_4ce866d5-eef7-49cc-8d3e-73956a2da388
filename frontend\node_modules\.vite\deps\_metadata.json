{"hash": "7dddacbb", "configHash": "6683d295", "lockfileHash": "c9ccda32", "browserHash": "ea9d8f2d", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "64a9f7f1", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "ff77a1cd", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "28a680f1", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "7ccf55c1", "needsInterop": true}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "5ef670c6", "needsInterop": false}, "@radix-ui/react-collapsible": {"src": "../../@radix-ui/react-collapsible/dist/index.mjs", "file": "@radix-ui_react-collapsible.js", "fileHash": "dbee4547", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "7a431d71", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "6c1bea9b", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "664e1c7d", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "a7712db0", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "770cac1a", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "cfe48437", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "e9a34f22", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "de6befd7", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "c52f9967", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "dba7927d", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "69c8cee1", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "22a37836", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "1fab9af9", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "7101ba77", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-46SBXC5E": {"file": "chunk-46SBXC5E.js"}, "chunk-BJLGYG7V": {"file": "chunk-BJLGYG7V.js"}, "chunk-HNBOXA6U": {"file": "chunk-HNBOXA6U.js"}, "chunk-U35UH4HV": {"file": "chunk-U35UH4HV.js"}, "chunk-C6M36NIQ": {"file": "chunk-C6M36NIQ.js"}, "chunk-4VTKDTNZ": {"file": "chunk-4VTKDTNZ.js"}, "chunk-U4EKBF5E": {"file": "chunk-U4EKBF5E.js"}, "chunk-FF6G2HSA": {"file": "chunk-FF6G2HSA.js"}, "chunk-MSY4FMPP": {"file": "chunk-MSY4FMPP.js"}, "chunk-JOBYYKYD": {"file": "chunk-JOBYYKYD.js"}, "chunk-IDV5Y7EL": {"file": "chunk-IDV5Y7EL.js"}, "chunk-IQRSJHQF": {"file": "chunk-IQRSJHQF.js"}, "chunk-OQSV7N3F": {"file": "chunk-OQSV7N3F.js"}, "chunk-D7552MD7": {"file": "chunk-D7552MD7.js"}, "chunk-OBYCLIUT": {"file": "chunk-OBYCLIUT.js"}, "chunk-BQYK6RGN": {"file": "chunk-BQYK6RGN.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}