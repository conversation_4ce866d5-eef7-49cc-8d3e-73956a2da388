/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M17 12H3", key: "8awo09" }],
  ["path", { d: "m11 18 6-6-6-6", key: "8c2y43" }],
  ["path", { d: "M21 5v14", key: "nzette" }]
];
const ArrowRightToLine = createLucideIcon("arrow-right-to-line", __iconNode);

export { __iconNode, ArrowRightToLine as default };
//# sourceMappingURL=arrow-right-to-line.js.map
