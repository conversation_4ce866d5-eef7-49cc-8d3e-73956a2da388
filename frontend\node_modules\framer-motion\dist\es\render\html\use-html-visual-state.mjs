import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';
import { createHtmlRenderState } from './utils/create-render-state.mjs';
import { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';

const useHTMLVisualState = /*@__PURE__*/ makeUseVisualState({
    scrapeMotionValuesFromProps,
    createRenderState: createHtmlRenderState,
});

export { useHTMLVisualState };
