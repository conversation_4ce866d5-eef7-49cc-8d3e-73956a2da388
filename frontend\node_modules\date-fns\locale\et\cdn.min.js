(()=>{var E;function O(H){return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},O(H)}function x(H,J){var X=Object.keys(H);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(H);J&&(Y=Y.filter(function(B){return Object.getOwnPropertyDescriptor(H,B).enumerable})),X.push.apply(X,Y)}return X}function C(H){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?x(Object(X),!0).forEach(function(Y){W(H,Y,X[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(X)):x(Object(X)).forEach(function(Y){Object.defineProperty(H,Y,Object.getOwnPropertyDescriptor(X,Y))})}return H}function W(H,J,X){if(J=D(J),J in H)Object.defineProperty(H,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else H[J]=X;return H}function D(H){var J=S(H,"string");return O(J)=="symbol"?J:String(J)}function S(H,J){if(O(H)!="object"||!H)return H;var X=H[Symbol.toPrimitive];if(X!==void 0){var Y=X.call(H,J||"default");if(O(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(H)}var M=Object.defineProperty,XH=function H(J,X){for(var Y in X)M(J,Y,{get:X[Y],enumerable:!0,configurable:!0,set:function B(Z){return X[Y]=function(){return Z}}})},A={lessThanXSeconds:{standalone:{one:"v\xE4hem kui \xFCks sekund",other:"v\xE4hem kui {{count}} sekundit"},withPreposition:{one:"v\xE4hem kui \xFChe sekundi",other:"v\xE4hem kui {{count}} sekundi"}},xSeconds:{standalone:{one:"\xFCks sekund",other:"{{count}} sekundit"},withPreposition:{one:"\xFChe sekundi",other:"{{count}} sekundi"}},halfAMinute:{standalone:"pool minutit",withPreposition:"poole minuti"},lessThanXMinutes:{standalone:{one:"v\xE4hem kui \xFCks minut",other:"v\xE4hem kui {{count}} minutit"},withPreposition:{one:"v\xE4hem kui \xFChe minuti",other:"v\xE4hem kui {{count}} minuti"}},xMinutes:{standalone:{one:"\xFCks minut",other:"{{count}} minutit"},withPreposition:{one:"\xFChe minuti",other:"{{count}} minuti"}},aboutXHours:{standalone:{one:"umbes \xFCks tund",other:"umbes {{count}} tundi"},withPreposition:{one:"umbes \xFChe tunni",other:"umbes {{count}} tunni"}},xHours:{standalone:{one:"\xFCks tund",other:"{{count}} tundi"},withPreposition:{one:"\xFChe tunni",other:"{{count}} tunni"}},xDays:{standalone:{one:"\xFCks p\xE4ev",other:"{{count}} p\xE4eva"},withPreposition:{one:"\xFChe p\xE4eva",other:"{{count}} p\xE4eva"}},aboutXWeeks:{standalone:{one:"umbes \xFCks n\xE4dal",other:"umbes {{count}} n\xE4dalat"},withPreposition:{one:"umbes \xFChe n\xE4dala",other:"umbes {{count}} n\xE4dala"}},xWeeks:{standalone:{one:"\xFCks n\xE4dal",other:"{{count}} n\xE4dalat"},withPreposition:{one:"\xFChe n\xE4dala",other:"{{count}} n\xE4dala"}},aboutXMonths:{standalone:{one:"umbes \xFCks kuu",other:"umbes {{count}} kuud"},withPreposition:{one:"umbes \xFChe kuu",other:"umbes {{count}} kuu"}},xMonths:{standalone:{one:"\xFCks kuu",other:"{{count}} kuud"},withPreposition:{one:"\xFChe kuu",other:"{{count}} kuu"}},aboutXYears:{standalone:{one:"umbes \xFCks aasta",other:"umbes {{count}} aastat"},withPreposition:{one:"umbes \xFChe aasta",other:"umbes {{count}} aasta"}},xYears:{standalone:{one:"\xFCks aasta",other:"{{count}} aastat"},withPreposition:{one:"\xFChe aasta",other:"{{count}} aasta"}},overXYears:{standalone:{one:"rohkem kui \xFCks aasta",other:"rohkem kui {{count}} aastat"},withPreposition:{one:"rohkem kui \xFChe aasta",other:"rohkem kui {{count}} aasta"}},almostXYears:{standalone:{one:"peaaegu \xFCks aasta",other:"peaaegu {{count}} aastat"},withPreposition:{one:"peaaegu \xFChe aasta",other:"peaaegu {{count}} aasta"}}},R=function H(J,X,Y){var B=Y!==null&&Y!==void 0&&Y.addSuffix?A[J].withPreposition:A[J].standalone,Z;if(typeof B==="string")Z=B;else if(X===1)Z=B.one;else Z=B.other.replace("{{count}}",String(X));if(Y!==null&&Y!==void 0&&Y.addSuffix)if(Y.comparison&&Y.comparison>0)return Z+" p\xE4rast";else return Z+" eest";return Z};function G(H){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=J.width?String(J.width):H.defaultWidth,Y=H.formats[X]||H.formats[H.defaultWidth];return Y}}var V={full:"EEEE, d. MMMM y",long:"d. MMMM y",medium:"d. MMM y",short:"dd.MM.y"},L={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},j={full:"{{date}} 'kell' {{time}}",long:"{{date}} 'kell' {{time}}",medium:"{{date}}. {{time}}",short:"{{date}}. {{time}}"},w={date:G({formats:V,defaultWidth:"full"}),time:G({formats:L,defaultWidth:"full"}),dateTime:G({formats:j,defaultWidth:"full"})},_={lastWeek:"'eelmine' eeee 'kell' p",yesterday:"'eile kell' p",today:"'t\xE4na kell' p",tomorrow:"'homme kell' p",nextWeek:"'j\xE4rgmine' eeee 'kell' p",other:"P"},f=function H(J,X,Y,B){return _[J]};function Q(H){return function(J,X){var Y=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",B;if(Y==="formatting"&&H.formattingValues){var Z=H.defaultFormattingWidth||H.defaultWidth,T=X!==null&&X!==void 0&&X.width?String(X.width):Z;B=H.formattingValues[T]||H.formattingValues[Z]}else{var U=H.defaultWidth,$=X!==null&&X!==void 0&&X.width?String(X.width):H.defaultWidth;B=H.values[$]||H.values[U]}var I=H.argumentCallback?H.argumentCallback(J):J;return B[I]}}var F={narrow:["e.m.a","m.a.j"],abbreviated:["e.m.a","m.a.j"],wide:["enne meie ajaarvamist","meie ajaarvamise j\xE4rgi"]},v={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1. kvartal","2. kvartal","3. kvartal","4. kvartal"]},N={narrow:["J","V","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jaan","veebr","m\xE4rts","apr","mai","juuni","juuli","aug","sept","okt","nov","dets"],wide:["jaanuar","veebruar","m\xE4rts","aprill","mai","juuni","juuli","august","september","oktoober","november","detsember"]},z={narrow:["P","E","T","K","N","R","L"],short:["P","E","T","K","N","R","L"],abbreviated:["p\xFChap.","esmasp.","teisip.","kolmap.","neljap.","reede.","laup."],wide:["p\xFChap\xE4ev","esmasp\xE4ev","teisip\xE4ev","kolmap\xE4ev","neljap\xE4ev","reede","laup\xE4ev"]},P={narrow:{am:"AM",pm:"PM",midnight:"kesk\xF6\xF6",noon:"keskp\xE4ev",morning:"hommik",afternoon:"p\xE4rastl\xF5una",evening:"\xF5htu",night:"\xF6\xF6"},abbreviated:{am:"AM",pm:"PM",midnight:"kesk\xF6\xF6",noon:"keskp\xE4ev",morning:"hommik",afternoon:"p\xE4rastl\xF5una",evening:"\xF5htu",night:"\xF6\xF6"},wide:{am:"AM",pm:"PM",midnight:"kesk\xF6\xF6",noon:"keskp\xE4ev",morning:"hommik",afternoon:"p\xE4rastl\xF5una",evening:"\xF5htu",night:"\xF6\xF6"}},k={narrow:{am:"AM",pm:"PM",midnight:"kesk\xF6\xF6l",noon:"keskp\xE4eval",morning:"hommikul",afternoon:"p\xE4rastl\xF5unal",evening:"\xF5htul",night:"\xF6\xF6sel"},abbreviated:{am:"AM",pm:"PM",midnight:"kesk\xF6\xF6l",noon:"keskp\xE4eval",morning:"hommikul",afternoon:"p\xE4rastl\xF5unal",evening:"\xF5htul",night:"\xF6\xF6sel"},wide:{am:"AM",pm:"PM",midnight:"kesk\xF6\xF6l",noon:"keskp\xE4eval",morning:"hommikul",afternoon:"p\xE4rastl\xF5unal",evening:"\xF5htul",night:"\xF6\xF6sel"}},b=function H(J,X){var Y=Number(J);return Y+"."},h={ordinalNumber:b,era:Q({values:F,defaultWidth:"wide"}),quarter:Q({values:v,defaultWidth:"wide",argumentCallback:function H(J){return J-1}}),month:Q({values:N,defaultWidth:"wide",formattingValues:N,defaultFormattingWidth:"wide"}),day:Q({values:z,defaultWidth:"wide",formattingValues:z,defaultFormattingWidth:"wide"}),dayPeriod:Q({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function q(H){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=X.width,B=Y&&H.matchPatterns[Y]||H.matchPatterns[H.defaultMatchWidth],Z=J.match(B);if(!Z)return null;var T=Z[0],U=Y&&H.parsePatterns[Y]||H.parsePatterns[H.defaultParseWidth],$=Array.isArray(U)?y(U,function(K){return K.test(T)}):m(U,function(K){return K.test(T)}),I;I=H.valueCallback?H.valueCallback($):$,I=X.valueCallback?X.valueCallback(I):I;var JH=J.slice(T.length);return{value:I,rest:JH}}}function m(H,J){for(var X in H)if(Object.prototype.hasOwnProperty.call(H,X)&&J(H[X]))return X;return}function y(H,J){for(var X=0;X<H.length;X++)if(J(H[X]))return X;return}function c(H){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=J.match(H.matchPattern);if(!Y)return null;var B=Y[0],Z=J.match(H.parsePattern);if(!Z)return null;var T=H.valueCallback?H.valueCallback(Z[0]):Z[0];T=X.valueCallback?X.valueCallback(T):T;var U=J.slice(B.length);return{value:T,rest:U}}}var d=/^\d+\./i,g=/\d+/i,p={narrow:/^(e\.m\.a|m\.a\.j|eKr|pKr)/i,abbreviated:/^(e\.m\.a|m\.a\.j|eKr|pKr)/i,wide:/^(enne meie ajaarvamist|meie ajaarvamise järgi|enne Kristust|pärast Kristust)/i},u={any:[/^e/i,/^(m|p)/i]},l={narrow:/^[1234]/i,abbreviated:/^K[1234]/i,wide:/^[1234](\.)? kvartal/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[jvmasond]/i,abbreviated:/^(jaan|veebr|märts|apr|mai|juuni|juuli|aug|sept|okt|nov|dets)/i,wide:/^(jaanuar|veebruar|märts|aprill|mai|juuni|juuli|august|september|oktoober|november|detsember)/i},s={narrow:[/^j/i,/^v/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^v/i,/^mär/i,/^ap/i,/^mai/i,/^juun/i,/^juul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},o={narrow:/^[petknrl]/i,short:/^[petknrl]/i,abbreviated:/^(püh?|esm?|tei?|kolm?|nel?|ree?|laup?)\.?/i,wide:/^(pühapäev|esmaspäev|teisipäev|kolmapäev|neljapäev|reede|laupäev)/i},r={any:[/^p/i,/^e/i,/^t/i,/^k/i,/^n/i,/^r/i,/^l/i]},e={any:/^(am|pm|keskööl?|keskpäev(al)?|hommik(ul)?|pärastlõunal?|õhtul?|öö(sel)?)/i},a={any:{am:/^a/i,pm:/^p/i,midnight:/^keskö/i,noon:/^keskp/i,morning:/hommik/i,afternoon:/pärastlõuna/i,evening:/õhtu/i,night:/öö/i}},t={ordinalNumber:c({matchPattern:d,parsePattern:g,valueCallback:function H(J){return parseInt(J,10)}}),era:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function H(J){return J+1}}),month:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},HH={code:"et",formatDistance:R,formatLong:w,formatRelative:f,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=C(C({},window.dateFns),{},{locale:C(C({},(E=window.dateFns)===null||E===void 0?void 0:E.locale),{},{et:HH})})})();

//# debugId=BD871DB44DD8225D64756E2164756E21
