const User = require('../models/User');
const Subscription = require('../models/Subscription');
const AttackMethod = require('../models/AttackMethod');
const logger = require('../utils/logger');
const { USER_ROLES } = require('../utils/constants');

// Get admin dashboard stats
const getDashboardStats = async (req, res) => {
  try {
    const [
      totalUsers,
      activeUsers,
      totalSubscriptions,
      activeSubscriptions,
      totalMethods,
      activeMethods
    ] = await Promise.all([
      User.countDocuments(),
      User.countDocuments({ is_active: true }),
      Subscription.countDocuments(),
      Subscription.countDocuments({ is_active: true }),
      AttackMethod.countDocuments(),
      AttackMethod.countDocuments({ is_active: true })
    ]);

    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('username role createdAt is_active');

    res.json({
      success: true,
      data: {
        stats: {
          totalUsers,
          activeUsers,
          totalSubscriptions,
          activeSubscriptions,
          totalMethods,
          activeMethods
        },
        recentUsers
      }
    });
  } catch (error) {
    logger.error('Admin dashboard stats error', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard stats'
    });
  }
};

// Get all users with pagination
const getUsers = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search || '';
    const role = req.query.role || '';

    const query = {};
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    if (role) {
      query.role = role;
    }

    const [users, total] = await Promise.all([
      User.find(query)
        .populate('subscription_id', 'name price')
        .select('-password_hash -verification_token -password_reset_token')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      User.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Get users error', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users'
    });
  }
};

// Update user
const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove sensitive fields that shouldn't be updated directly
    delete updates.password_hash;
    delete updates.api_key;
    delete updates._id;

    const user = await User.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    ).select('-password_hash -verification_token -password_reset_token');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    logger.info('User updated by admin', {
      adminId: req.user._id,
      userId: id,
      updates: Object.keys(updates)
    });

    res.json({
      success: true,
      data: user,
      message: 'User updated successfully'
    });
  } catch (error) {
    logger.error('Update user error', { error: error.message, userId: req.params.id });
    res.status(500).json({
      success: false,
      message: 'Failed to update user'
    });
  }
};

// Delete user
const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // Prevent admin from deleting themselves
    if (id === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete your own account'
      });
    }

    const user = await User.findByIdAndDelete(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    logger.info('User deleted by admin', {
      adminId: req.user._id,
      deletedUserId: id,
      deletedUsername: user.username
    });

    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    logger.error('Delete user error', { error: error.message, userId: req.params.id });
    res.status(500).json({
      success: false,
      message: 'Failed to delete user'
    });
  }
};

// Get all subscriptions
const getSubscriptions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [subscriptions, total] = await Promise.all([
      Subscription.find()
        .populate('created_by', 'username')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      Subscription.countDocuments()
    ]);

    res.json({
      success: true,
      data: {
        subscriptions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Get subscriptions error', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to fetch subscriptions'
    });
  }
};

// Create subscription
const createSubscription = async (req, res) => {
  try {
    const subscriptionData = {
      ...req.body,
      created_by: req.user._id
    };

    const subscription = new Subscription(subscriptionData);
    await subscription.save();

    logger.info('Subscription created by admin', {
      adminId: req.user._id,
      subscriptionId: subscription._id,
      subscriptionName: subscription.name
    });

    res.status(201).json({
      success: true,
      data: subscription,
      message: 'Subscription created successfully'
    });
  } catch (error) {
    logger.error('Create subscription error', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to create subscription'
    });
  }
};

// Update subscription
const updateSubscription = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    delete updates._id;
    delete updates.created_by;

    const subscription = await Subscription.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    );

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Subscription not found'
      });
    }

    logger.info('Subscription updated by admin', {
      adminId: req.user._id,
      subscriptionId: id,
      updates: Object.keys(updates)
    });

    res.json({
      success: true,
      data: subscription,
      message: 'Subscription updated successfully'
    });
  } catch (error) {
    logger.error('Update subscription error', { error: error.message, subscriptionId: req.params.id });
    res.status(500).json({
      success: false,
      message: 'Failed to update subscription'
    });
  }
};

// Delete subscription
const deleteSubscription = async (req, res) => {
  try {
    const { id } = req.params;

    const subscription = await Subscription.findByIdAndDelete(id);

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Subscription not found'
      });
    }

    logger.info('Subscription deleted by admin', {
      adminId: req.user._id,
      deletedSubscriptionId: id,
      deletedSubscriptionName: subscription.name
    });

    res.json({
      success: true,
      message: 'Subscription deleted successfully'
    });
  } catch (error) {
    logger.error('Delete subscription error', { error: error.message, subscriptionId: req.params.id });
    res.status(500).json({
      success: false,
      message: 'Failed to delete subscription'
    });
  }
};

// Get all attack methods
const getAttackMethods = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [methods, total] = await Promise.all([
      AttackMethod.find()
        .populate('created_by', 'username')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      AttackMethod.countDocuments()
    ]);

    res.json({
      success: true,
      data: {
        methods,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Get attack methods error', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attack methods'
    });
  }
};

// Create attack method
const createAttackMethod = async (req, res) => {
  try {
    const methodData = {
      ...req.body,
      created_by: req.user._id
    };

    const method = new AttackMethod(methodData);
    await method.save();

    logger.info('Attack method created by admin', {
      adminId: req.user._id,
      methodId: method._id,
      methodName: method.name
    });

    res.status(201).json({
      success: true,
      data: method,
      message: 'Attack method created successfully'
    });
  } catch (error) {
    logger.error('Create attack method error', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to create attack method'
    });
  }
};

// Update attack method
const updateAttackMethod = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    delete updates._id;
    delete updates.created_by;

    const method = await AttackMethod.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    );

    if (!method) {
      return res.status(404).json({
        success: false,
        message: 'Attack method not found'
      });
    }

    logger.info('Attack method updated by admin', {
      adminId: req.user._id,
      methodId: id,
      updates: Object.keys(updates)
    });

    res.json({
      success: true,
      data: method,
      message: 'Attack method updated successfully'
    });
  } catch (error) {
    logger.error('Update attack method error', { error: error.message, methodId: req.params.id });
    res.status(500).json({
      success: false,
      message: 'Failed to update attack method'
    });
  }
};

// Delete attack method
const deleteAttackMethod = async (req, res) => {
  try {
    const { id } = req.params;

    const method = await AttackMethod.findByIdAndDelete(id);

    if (!method) {
      return res.status(404).json({
        success: false,
        message: 'Attack method not found'
      });
    }

    logger.info('Attack method deleted by admin', {
      adminId: req.user._id,
      deletedMethodId: id,
      deletedMethodName: method.name
    });

    res.json({
      success: true,
      message: 'Attack method deleted successfully'
    });
  } catch (error) {
    logger.error('Delete attack method error', { error: error.message, methodId: req.params.id });
    res.status(500).json({
      success: false,
      message: 'Failed to delete attack method'
    });
  }
};

// Get site settings (placeholder for future implementation)
const getSiteSettings = async (req, res) => {
  try {
    // This would typically fetch from a SiteSettings model
    const settings = {
      siteName: 'STRESSER.BA',
      siteDescription: 'Professional Network Testing Platform',
      keywords: 'stresser, ddos, network testing, security',
      logo: '/logo.png',
      maintenanceMode: false,
      registrationEnabled: true,
      maxAttackDuration: 3600,
      defaultCooldown: 60
    };

    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    logger.error('Get site settings error', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to fetch site settings'
    });
  }
};

// Update site settings (placeholder for future implementation)
const updateSiteSettings = async (req, res) => {
  try {
    const updates = req.body;

    // This would typically update a SiteSettings model
    logger.info('Site settings updated by admin', {
      adminId: req.user._id,
      updates: Object.keys(updates)
    });

    res.json({
      success: true,
      data: updates,
      message: 'Site settings updated successfully'
    });
  } catch (error) {
    logger.error('Update site settings error', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to update site settings'
    });
  }
};

module.exports = {
  getDashboardStats,
  getUsers,
  updateUser,
  deleteUser,
  getSubscriptions,
  createSubscription,
  updateSubscription,
  deleteSubscription,
  getAttackMethods,
  createAttackMethod,
  updateAttackMethod,
  deleteAttackMethod,
  getSiteSettings,
  updateSiteSettings
};
