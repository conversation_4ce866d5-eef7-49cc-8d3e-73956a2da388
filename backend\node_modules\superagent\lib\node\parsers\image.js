"use strict";

module.exports = (res, fn) => {
  const data = []; // Binary data needs binary storage

  res.on('data', chunk => {
    data.push(chunk);
  });
  res.on('end', () => {
    fn(null, Buffer.concat(data));
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwicmVzIiwiZm4iLCJkYXRhIiwib24iLCJjaHVuayIsInB1c2giLCJCdWZmZXIiLCJjb25jYXQiXSwic291cmNlcyI6WyIuLi8uLi8uLi9zcmMvbm9kZS9wYXJzZXJzL2ltYWdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gKHJlcywgZm4pID0+IHtcbiAgY29uc3QgZGF0YSA9IFtdOyAvLyBCaW5hcnkgZGF0YSBuZWVkcyBiaW5hcnkgc3RvcmFnZVxuXG4gIHJlcy5vbignZGF0YScsIChjaHVuaykgPT4ge1xuICAgIGRhdGEucHVzaChjaHVuayk7XG4gIH0pO1xuICByZXMub24oJ2VuZCcsICgpID0+IHtcbiAgICBmbihudWxsLCBCdWZmZXIuY29uY2F0KGRhdGEpKTtcbiAgfSk7XG59O1xuIl0sIm1hcHBpbmdzIjoiOztBQUFBQSxNQUFNLENBQUNDLE9BQU8sR0FBRyxDQUFDQyxHQUFHLEVBQUVDLEVBQUUsS0FBSztFQUM1QixNQUFNQyxJQUFJLEdBQUcsRUFBRSxDQUFDLENBQUM7O0VBRWpCRixHQUFHLENBQUNHLEVBQUUsQ0FBQyxNQUFNLEVBQUdDLEtBQUssSUFBSztJQUN4QkYsSUFBSSxDQUFDRyxJQUFJLENBQUNELEtBQUssQ0FBQztFQUNsQixDQUFDLENBQUM7RUFDRkosR0FBRyxDQUFDRyxFQUFFLENBQUMsS0FBSyxFQUFFLE1BQU07SUFDbEJGLEVBQUUsQ0FBQyxJQUFJLEVBQUVLLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDTCxJQUFJLENBQUMsQ0FBQztFQUMvQixDQUFDLENBQUM7QUFDSixDQUFDIiwiaWdub3JlTGlzdCI6W119