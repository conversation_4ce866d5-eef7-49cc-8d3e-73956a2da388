{"version": 3, "file": "standard-schema.modern.mjs", "sources": ["../src/standard-schema.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { StandardSchemaV1 } from '@standard-schema/spec';\nimport { getDotPath } from '@standard-schema/utils';\nimport { FieldError, FieldValues, Resolver } from 'react-hook-form';\n\nfunction parseErrorSchema(\n  issues: readonly StandardSchemaV1.Issue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n\n  for (let i = 0; i < issues.length; i++) {\n    const error = issues[i];\n    const path = getDotPath(error);\n\n    if (path) {\n      if (!errors[path]) {\n        errors[path] = { message: error.message, type: '' };\n      }\n\n      if (validateAllFieldCriteria) {\n        const types = errors[path].types || {};\n\n        errors[path].types = {\n          ...types,\n          [Object.keys(types).length]: error.message,\n        };\n      }\n    }\n  }\n\n  return errors;\n}\n\nexport function standardSchemaResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions?: {\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function standardSchemaResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions: never | undefined,\n  resolverOptions: {\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form that validates data using a Standard Schema.\n *\n * @param {Schema} schema - The Standard Schema to validate against\n * @param {Object} resolverOptions - Options for the resolver\n * @param {boolean} [resolverOptions.raw=false] - Whether to return raw input values instead of parsed values\n * @returns {Resolver} A resolver function compatible with react-hook-form\n *\n * @example\n * ```ts\n * const schema = z.object({\n *   name: z.string().min(2),\n *   age: z.number().min(18)\n * });\n *\n * useForm({\n *   resolver: standardSchemaResolver(schema)\n * });\n * ```\n */\nexport function standardSchemaResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions: {\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Output | Input> {\n  return async (values, _, options) => {\n    let result = schema['~standard'].validate(values);\n    if (result instanceof Promise) {\n      result = await result;\n    }\n\n    if (result.issues) {\n      const errors = parseErrorSchema(\n        result.issues,\n        !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n      );\n\n      return {\n        values: {},\n        errors: toNestErrors(errors, options),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.raw ? Object.assign({}, values) : result.value,\n      errors: {},\n    };\n  };\n}\n"], "names": ["standardSchemaResolver", "schema", "_schemaOptions", "resolverOptions", "values", "_", "options", "result", "validate", "Promise", "issues", "errors", "validateAllFieldCriteria", "i", "length", "error", "path", "getDotPath", "message", "type", "types", "_extends", "Object", "keys", "parseErrorSchema", "shouldUseNativeValidation", "criteriaMode", "toNestErrors", "validateFieldsNatively", "raw", "assign", "value"], "mappings": "2VA8EgB,SAAAA,EAKdC,EACAC,EACAC,EAEI,CAAE,GAEN,OAAcC,MAAAA,EAAQC,EAAGC,KACvB,IAAIC,EAASN,EAAO,aAAaO,SAASJ,GAK1C,GAJIG,aAAkBE,UACpBF,QAAeA,GAGbA,EAAOG,OAAQ,CACjB,MAAMC,EA3FZ,SACED,EACAE,GAEA,MAAMD,EAAqC,CAAA,EAE3C,IAAK,IAAIE,EAAI,EAAGA,EAAIH,EAAOI,OAAQD,IAAK,CACtC,MAAME,EAAQL,EAAOG,GACfG,EAAOC,EAAWF,GAExB,GAAIC,IACGL,EAAOK,KACVL,EAAOK,GAAQ,CAAEE,QAASH,EAAMG,QAASC,KAAM,KAG7CP,GAA0B,CAC5B,MAAMQ,EAAQT,EAAOK,GAAMI,OAAS,CAAA,EAEpCT,EAAOK,GAAMI,MAAKC,KACbD,EAAK,CACR,CAACE,OAAOC,KAAKH,GAAON,QAASC,EAAMG,SAEvC,CAEJ,CAEA,OAAOP,CACT,CAgEqBa,CACbjB,EAAOG,QACNJ,EAAQmB,2BAAsD,QAAzBnB,EAAQoB,cAGhD,MAAO,CACLtB,OAAQ,CAAE,EACVO,OAAQgB,EAAahB,EAAQL,GAEjC,CAIA,OAFAA,EAAQmB,2BAA6BG,EAAuB,CAAE,EAAEtB,GAEzD,CACLF,OAAQD,EAAgB0B,IAAMP,OAAOQ,OAAO,CAAA,EAAI1B,GAAUG,EAAOwB,MACjEpB,OAAQ,IAGd"}