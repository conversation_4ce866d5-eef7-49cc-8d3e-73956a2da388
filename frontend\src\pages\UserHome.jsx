import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Home,
  Menu,
  Zap,
  Activity,
  Crown,
  Calendar,
  Clock,
  Code,
  User
} from 'lucide-react';

const UserHome = () => {
  const { user } = useAuth();
  
  // Sample news data matching the image
  const [news] = useState([
    {
      id: 1,
      title: "🔥 UPDATES & IMPROVEMENTS TO LAYER 4 🔥",
      description: "The whole Layer-4 has been revamped and improved, all the previous methods were replaced with fresh and powerful new methods!",
      date: "Mar 10, 2025, 5:36 PM",
      type: "update"
    },
    {
      id: 2,
      title: "🌟 TCP-CN",
      description: "This method generates pure chinese traffic, very useful against targets which geoblock and restrict the access to only chinese IPs.",
      note: "It is highly recommended to target an open TCP port when using this method.",
      date: "Mar 10, 2025, 5:35 PM",
      type: "method"
    },
    {
      id: 3,
      title: "🌟 UDP-PRIVATE",
      description: "Highly improved UDP Method, much more efficient than the previous methods.",
      discount: "Use this discount code to try it out! — DISCOUNT CODE → UP20 ← 20% 折扣代码",
      date: "Mar 10, 2025, 5:34 PM",
      type: "method"
    }
  ]);

  const getSubscriptionInfo = () => {
    const subscription = user?.subscription || 'Free Plan';
    switch (subscription) {
      case 'Free Package':
      case 'Free Plan':
        return { 
          name: 'Free Plan', 
          maxTime: 300, 
          concurrents: 1, 
          api: false,
          color: 'text-gray-400'
        };
      case 'Premium #1':
        return { 
          name: 'Premium #1', 
          maxTime: 1200, 
          concurrents: 2, 
          api: true,
          color: 'text-yellow-400'
        };
      case 'Premium #5':
        return { 
          name: 'Premium #5', 
          maxTime: 7200, 
          concurrents: 10, 
          api: true,
          color: 'text-purple-400'
        };
      default:
        return { 
          name: 'Free Plan', 
          maxTime: 300, 
          concurrents: 1, 
          api: false,
          color: 'text-gray-400'
        };
    }
  };

  const subscriptionInfo = getSubscriptionInfo();

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
            <p className="text-muted-foreground">Please log in to access this page.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Home className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-3xl font-bold">Home</h1>
                <p className="text-muted-foreground">Welcome {user.username}, find the latest updates here.</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="secondary" className="flex items-center space-x-1">
                <Crown className="h-3 w-3" />
                <span>{subscriptionInfo.name}</span>
              </Badge>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                <span>Online</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Left Side - Recent News */}
          <div className="lg:col-span-2">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-primary mb-2">Recent News</h2>
            </div>

            <div className="space-y-6">
              {news.map((item) => (
                <Card key={item.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        {item.type === 'update' && (
                          <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                            <Zap className="h-4 w-4 text-white" />
                          </div>
                        )}
                        {item.type === 'method' && (
                          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <Activity className="h-4 w-4 text-white" />
                          </div>
                        )}
                      </div>

                      <div className="flex-1">
                        <h3 className="text-lg font-semibold mb-2">
                          {item.title}
                        </h3>
                        <p className="text-muted-foreground mb-3 leading-relaxed">
                          {item.description}
                        </p>

                        {item.note && (
                          <p className="text-muted-foreground text-sm mb-3 italic">
                            {item.note}
                          </p>
                        )}

                        {item.discount && (
                          <div className="bg-muted border rounded-lg p-3 mb-3">
                            <p className="text-primary text-sm font-medium">
                              {item.discount}
                            </p>
                          </div>
                        )}

                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground text-sm">{item.date}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Right Side - Account Overview */}
          <div className="space-y-6">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-primary mb-2">Account Overview</h2>
            </div>

            {/* Current Plan */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Crown className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">
                      {subscriptionInfo.name}
                    </h3>
                    <p className="text-muted-foreground text-sm">Current Plan</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Expire */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-muted rounded-lg flex items-center justify-center">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-muted-foreground">n/a</h3>
                    <p className="text-muted-foreground text-sm">Expire</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Attack Time */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-500/10 rounded-lg flex items-center justify-center">
                    <Clock className="h-4 w-4 text-blue-500" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{subscriptionInfo.maxTime}</h3>
                    <p className="text-muted-foreground text-sm">Attack Time</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Concurrents */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-500/10 rounded-lg flex items-center justify-center">
                    <Activity className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{subscriptionInfo.concurrents}</h3>
                    <p className="text-muted-foreground text-sm">Concurrents</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* API */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-purple-500/10 rounded-lg flex items-center justify-center">
                    <Code className="h-4 w-4 text-purple-500" />
                  </div>
                  <div>
                    <h3 className={`text-lg font-semibold ${subscriptionInfo.api ? 'text-green-500' : 'text-red-500'}`}>
                      {subscriptionInfo.api ? 'True' : 'False'}
                    </h3>
                    <p className="text-muted-foreground text-sm">API</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserHome;
