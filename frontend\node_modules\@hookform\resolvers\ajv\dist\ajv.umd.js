!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("ajv"),require("ajv-errors"),require("ajv-formats"),require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","ajv","ajv-errors","ajv-formats","react-hook-form"],r):r((e||self).hookformResolversAjv={},e.hookformResolvers,e.ajv,e.ajvErrors,e.ajvFormats,e.ReactHookForm)}(this,function(e,r,o,a,s,t){function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=/*#__PURE__*/i(o),f=/*#__PURE__*/i(a),l=/*#__PURE__*/i(s),u=function(e,r){for(var o={},a=function(e){"required"===e.keyword&&(e.instancePath+="/"+e.params.missingProperty);var a=e.instancePath.substring(1).replace(/\//g,".");if(o[a]||(o[a]={message:e.message,type:e.keyword}),r){var s=o[a].types,i=s&&s[e.keyword];o[a]=t.appendErrors(a,r,o,e.keyword,i?[].concat(i,e.message||""):e.message)}},s=function(){var r=e[i];"errorMessage"===r.keyword?r.params.errors.forEach(function(e){e.message=r.message,a(e)}):a(r)},i=0;i<e.length;i+=1)s();return o};e.ajvResolver=function(e,o,a){return void 0===a&&(a={}),function(s,t,i){try{var v=new n.default(Object.assign({},{allErrors:!0,validateSchema:!0},o));f.default(v),l.default(v);var c=v.compile(Object.assign({$async:a&&"async"===a.mode},e)),d=c(s);return i.shouldUseNativeValidation&&r.validateFieldsNatively({},i),Promise.resolve(d?{values:s,errors:{}}:{values:{},errors:r.toNestErrors(u(c.errors,!i.shouldUseNativeValidation&&"all"===i.criteriaMode),i)})}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=ajv.umd.js.map
