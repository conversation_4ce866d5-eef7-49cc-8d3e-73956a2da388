import { formatDistance } from "./uz-Cyrl/_lib/formatDistance.js";
import { formatLong } from "./uz-Cyrl/_lib/formatLong.js";
import { formatRelative } from "./uz-Cyrl/_lib/formatRelative.js";
import { localize } from "./uz-Cyrl/_lib/localize.js";
import { match } from "./uz-Cyrl/_lib/match.js";

/**
 * @category Locales
 * @summary Uzbek Cyrillic locale.
 * @language Uzbek
 * @iso-639-2 uzb
 * <AUTHOR> [@kamronbek28](https://github.com/kamronbek28)
 */
export const uzCyrl = {
  code: "uz-Cyrl",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default uzCyrl;
