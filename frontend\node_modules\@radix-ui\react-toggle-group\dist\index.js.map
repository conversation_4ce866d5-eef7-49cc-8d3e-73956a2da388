{"version": 3, "sources": ["../src/index.ts", "../src/toggle-group.tsx"], "sourcesContent": ["'use client';\nexport {\n  createToggleGroupScope,\n  //\n  ToggleGroup,\n  ToggleGroupItem,\n  //\n  Root,\n  Item,\n} from './toggle-group';\nexport type {\n  ToggleGroupSingleProps,\n  ToggleGroupMultipleProps,\n  ToggleGroupItemProps,\n} from './toggle-group';\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Toggle } from '@radix-ui/react-toggle';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * ToggleGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOGGLE_GROUP_NAME = 'ToggleGroup';\n\ntype ScopedProps<P> = P & { __scopeToggleGroup?: Scope };\nconst [createToggleGroupContext, createToggleGroupScope] = createContextScope(TOGGLE_GROUP_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype ToggleGroupElement = ToggleGroupImplSingleElement | ToggleGroupImplMultipleElement;\ninterface ToggleGroupSingleProps extends ToggleGroupImplSingleProps {\n  type: 'single';\n}\ninterface ToggleGroupMultipleProps extends ToggleGroupImplMultipleProps {\n  type: 'multiple';\n}\n\nconst ToggleGroup = React.forwardRef<\n  ToggleGroupElement,\n  ToggleGroupSingleProps | ToggleGroupMultipleProps\n>((props, forwardedRef) => {\n  const { type, ...toggleGroupProps } = props;\n\n  if (type === 'single') {\n    const singleProps = toggleGroupProps as ToggleGroupImplSingleProps;\n    return <ToggleGroupImplSingle {...singleProps} ref={forwardedRef} />;\n  }\n\n  if (type === 'multiple') {\n    const multipleProps = toggleGroupProps as ToggleGroupImplMultipleProps;\n    return <ToggleGroupImplMultiple {...multipleProps} ref={forwardedRef} />;\n  }\n\n  throw new Error(`Missing prop \\`type\\` expected on \\`${TOGGLE_GROUP_NAME}\\``);\n});\n\nToggleGroup.displayName = TOGGLE_GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ToggleGroupValueContextValue = {\n  type: 'single' | 'multiple';\n  value: string[];\n  onItemActivate(value: string): void;\n  onItemDeactivate(value: string): void;\n};\n\nconst [ToggleGroupValueProvider, useToggleGroupValueContext] =\n  createToggleGroupContext<ToggleGroupValueContextValue>(TOGGLE_GROUP_NAME);\n\ntype ToggleGroupImplSingleElement = ToggleGroupImplElement;\ninterface ToggleGroupImplSingleProps extends ToggleGroupImplProps {\n  /**\n   * The controlled stateful value of the item that is pressed.\n   */\n  value?: string;\n  /**\n   * The value of the item that is pressed when initially rendered. Use\n   * `defaultValue` if you do not need to control the state of a toggle group.\n   */\n  defaultValue?: string;\n  /**\n   * The callback that fires when the value of the toggle group changes.\n   */\n  onValueChange?(value: string): void;\n}\n\nconst ToggleGroupImplSingle = React.forwardRef<\n  ToggleGroupImplSingleElement,\n  ToggleGroupImplSingleProps\n>((props: ScopedProps<ToggleGroupImplSingleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...toggleGroupSingleProps\n  } = props;\n\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? '',\n    onChange: onValueChange,\n    caller: TOGGLE_GROUP_NAME,\n  });\n\n  return (\n    <ToggleGroupValueProvider\n      scope={props.__scopeToggleGroup}\n      type=\"single\"\n      value={React.useMemo(() => (value ? [value] : []), [value])}\n      onItemActivate={setValue}\n      onItemDeactivate={React.useCallback(() => setValue(''), [setValue])}\n    >\n      <ToggleGroupImpl {...toggleGroupSingleProps} ref={forwardedRef} />\n    </ToggleGroupValueProvider>\n  );\n});\n\ntype ToggleGroupImplMultipleElement = ToggleGroupImplElement;\ninterface ToggleGroupImplMultipleProps extends ToggleGroupImplProps {\n  /**\n   * The controlled stateful value of the items that are pressed.\n   */\n  value?: string[];\n  /**\n   * The value of the items that are pressed when initially rendered. Use\n   * `defaultValue` if you do not need to control the state of a toggle group.\n   */\n  defaultValue?: string[];\n  /**\n   * The callback that fires when the state of the toggle group changes.\n   */\n  onValueChange?(value: string[]): void;\n}\n\nconst ToggleGroupImplMultiple = React.forwardRef<\n  ToggleGroupImplMultipleElement,\n  ToggleGroupImplMultipleProps\n>((props: ScopedProps<ToggleGroupImplMultipleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...toggleGroupMultipleProps\n  } = props;\n\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? [],\n    onChange: onValueChange,\n    caller: TOGGLE_GROUP_NAME,\n  });\n\n  const handleButtonActivate = React.useCallback(\n    (itemValue: string) => setValue((prevValue = []) => [...prevValue, itemValue]),\n    [setValue]\n  );\n\n  const handleButtonDeactivate = React.useCallback(\n    (itemValue: string) =>\n      setValue((prevValue = []) => prevValue.filter((value) => value !== itemValue)),\n    [setValue]\n  );\n\n  return (\n    <ToggleGroupValueProvider\n      scope={props.__scopeToggleGroup}\n      type=\"multiple\"\n      value={value}\n      onItemActivate={handleButtonActivate}\n      onItemDeactivate={handleButtonDeactivate}\n    >\n      <ToggleGroupImpl {...toggleGroupMultipleProps} ref={forwardedRef} />\n    </ToggleGroupValueProvider>\n  );\n});\n\nToggleGroup.displayName = TOGGLE_GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ToggleGroupContextValue = { rovingFocus: boolean; disabled: boolean };\n\nconst [ToggleGroupContext, useToggleGroupContext] =\n  createToggleGroupContext<ToggleGroupContextValue>(TOGGLE_GROUP_NAME);\n\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype ToggleGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ToggleGroupImplProps extends PrimitiveDivProps {\n  /**\n   * Whether the group is disabled from user interaction.\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * Whether the group should maintain roving focus of its buttons.\n   * @defaultValue true\n   */\n  rovingFocus?: boolean;\n  loop?: RovingFocusGroupProps['loop'];\n  orientation?: RovingFocusGroupProps['orientation'];\n  dir?: RovingFocusGroupProps['dir'];\n}\n\nconst ToggleGroupImpl = React.forwardRef<ToggleGroupImplElement, ToggleGroupImplProps>(\n  (props: ScopedProps<ToggleGroupImplProps>, forwardedRef) => {\n    const {\n      __scopeToggleGroup,\n      disabled = false,\n      rovingFocus = true,\n      orientation,\n      dir,\n      loop = true,\n      ...toggleGroupProps\n    } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeToggleGroup);\n    const direction = useDirection(dir);\n    const commonProps = { role: 'group', dir: direction, ...toggleGroupProps };\n    return (\n      <ToggleGroupContext scope={__scopeToggleGroup} rovingFocus={rovingFocus} disabled={disabled}>\n        {rovingFocus ? (\n          <RovingFocusGroup.Root\n            asChild\n            {...rovingFocusGroupScope}\n            orientation={orientation}\n            dir={direction}\n            loop={loop}\n          >\n            <Primitive.div {...commonProps} ref={forwardedRef} />\n          </RovingFocusGroup.Root>\n        ) : (\n          <Primitive.div {...commonProps} ref={forwardedRef} />\n        )}\n      </ToggleGroupContext>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * ToggleGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'ToggleGroupItem';\n\ntype ToggleGroupItemElement = ToggleGroupItemImplElement;\ninterface ToggleGroupItemProps extends Omit<ToggleGroupItemImplProps, 'pressed'> {}\n\nconst ToggleGroupItem = React.forwardRef<ToggleGroupItemElement, ToggleGroupItemProps>(\n  (props: ScopedProps<ToggleGroupItemProps>, forwardedRef) => {\n    const valueContext = useToggleGroupValueContext(ITEM_NAME, props.__scopeToggleGroup);\n    const context = useToggleGroupContext(ITEM_NAME, props.__scopeToggleGroup);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(props.__scopeToggleGroup);\n    const pressed = valueContext.value.includes(props.value);\n    const disabled = context.disabled || props.disabled;\n    const commonProps = { ...props, pressed, disabled };\n    const ref = React.useRef<HTMLDivElement>(null);\n    return context.rovingFocus ? (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={pressed}\n        ref={ref}\n      >\n        <ToggleGroupItemImpl {...commonProps} ref={forwardedRef} />\n      </RovingFocusGroup.Item>\n    ) : (\n      <ToggleGroupItemImpl {...commonProps} ref={forwardedRef} />\n    );\n  }\n);\n\nToggleGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ToggleGroupItemImplElement = React.ComponentRef<typeof Toggle>;\ntype ToggleProps = React.ComponentPropsWithoutRef<typeof Toggle>;\ninterface ToggleGroupItemImplProps extends Omit<ToggleProps, 'defaultPressed' | 'onPressedChange'> {\n  /**\n   * A string value for the toggle group item. All items within a toggle group should use a unique value.\n   */\n  value: string;\n}\n\nconst ToggleGroupItemImpl = React.forwardRef<ToggleGroupItemImplElement, ToggleGroupItemImplProps>(\n  (props: ScopedProps<ToggleGroupItemImplProps>, forwardedRef) => {\n    const { __scopeToggleGroup, value, ...itemProps } = props;\n    const valueContext = useToggleGroupValueContext(ITEM_NAME, __scopeToggleGroup);\n    const singleProps = { role: 'radio', 'aria-checked': props.pressed, 'aria-pressed': undefined };\n    const typeProps = valueContext.type === 'single' ? singleProps : undefined;\n    return (\n      <Toggle\n        {...typeProps}\n        {...itemProps}\n        ref={forwardedRef}\n        onPressedChange={(pressed) => {\n          if (pressed) {\n            valueContext.onItemActivate(value);\n          } else {\n            valueContext.onItemDeactivate(value);\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = ToggleGroup;\nconst Item = ToggleGroupItem;\n\nexport {\n  createToggleGroupScope,\n  //\n  ToggleGroup,\n  ToggleGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { ToggleGroupSingleProps, ToggleGroupMultipleProps, ToggleGroupItemProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,cAAAA;AAAA,EAAA,YAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,mBAAkB;AAClB,2BAAmC;AACnC,6BAA0B;AAC1B,uBAAkC;AAClC,gCAA4C;AAC5C,0BAAuB;AACvB,0CAAqC;AACrC,6BAA6B;AAgClB;AAxBX,IAAM,oBAAoB;AAG1B,IAAM,CAAC,0BAA0B,sBAAsB,QAAI,yCAAmB,mBAAmB;AAAA,EAC/F;AACF,CAAC;AACD,IAAM,+BAA2B,uDAA4B;AAU7D,IAAM,cAAc,aAAAC,QAAM,WAGxB,CAAC,OAAO,iBAAiB;AACzB,QAAM,EAAE,MAAM,GAAG,iBAAiB,IAAI;AAEtC,MAAI,SAAS,UAAU;AACrB,UAAM,cAAc;AACpB,WAAO,4CAAC,yBAAuB,GAAG,aAAa,KAAK,cAAc;AAAA,EACpE;AAEA,MAAI,SAAS,YAAY;AACvB,UAAM,gBAAgB;AACtB,WAAO,4CAAC,2BAAyB,GAAG,eAAe,KAAK,cAAc;AAAA,EACxE;AAEA,QAAM,IAAI,MAAM,uCAAuC,iBAAiB,IAAI;AAC9E,CAAC;AAED,YAAY,cAAc;AAW1B,IAAM,CAAC,0BAA0B,0BAA0B,IACzD,yBAAuD,iBAAiB;AAmB1E,IAAM,wBAAwB,aAAAA,QAAM,WAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM;AAAA,IACJ,OAAO;AAAA,IACP;AAAA,IACA,gBAAgB,MAAM;AAAA,IAAC;AAAA,IACvB,GAAG;AAAA,EACL,IAAI;AAEJ,QAAM,CAAC,OAAO,QAAQ,QAAI,0DAAqB;AAAA,IAC7C,MAAM;AAAA,IACN,aAAa,gBAAgB;AAAA,IAC7B,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AAED,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,MAAM;AAAA,MACb,MAAK;AAAA,MACL,OAAO,aAAAA,QAAM,QAAQ,MAAO,QAAQ,CAAC,KAAK,IAAI,CAAC,GAAI,CAAC,KAAK,CAAC;AAAA,MAC1D,gBAAgB;AAAA,MAChB,kBAAkB,aAAAA,QAAM,YAAY,MAAM,SAAS,EAAE,GAAG,CAAC,QAAQ,CAAC;AAAA,MAElE,sDAAC,mBAAiB,GAAG,wBAAwB,KAAK,cAAc;AAAA;AAAA,EAClE;AAEJ,CAAC;AAmBD,IAAM,0BAA0B,aAAAA,QAAM,WAGpC,CAAC,OAAkD,iBAAiB;AACpE,QAAM;AAAA,IACJ,OAAO;AAAA,IACP;AAAA,IACA,gBAAgB,MAAM;AAAA,IAAC;AAAA,IACvB,GAAG;AAAA,EACL,IAAI;AAEJ,QAAM,CAAC,OAAO,QAAQ,QAAI,0DAAqB;AAAA,IAC7C,MAAM;AAAA,IACN,aAAa,gBAAgB,CAAC;AAAA,IAC9B,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AAED,QAAM,uBAAuB,aAAAA,QAAM;AAAA,IACjC,CAAC,cAAsB,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,WAAW,SAAS,CAAC;AAAA,IAC7E,CAAC,QAAQ;AAAA,EACX;AAEA,QAAM,yBAAyB,aAAAA,QAAM;AAAA,IACnC,CAAC,cACC,SAAS,CAAC,YAAY,CAAC,MAAM,UAAU,OAAO,CAACC,WAAUA,WAAU,SAAS,CAAC;AAAA,IAC/E,CAAC,QAAQ;AAAA,EACX;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,MAAM;AAAA,MACb,MAAK;AAAA,MACL;AAAA,MACA,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAElB,sDAAC,mBAAiB,GAAG,0BAA0B,KAAK,cAAc;AAAA;AAAA,EACpE;AAEJ,CAAC;AAED,YAAY,cAAc;AAM1B,IAAM,CAAC,oBAAoB,qBAAqB,IAC9C,yBAAkD,iBAAiB;AAqBrE,IAAM,kBAAkB,aAAAD,QAAM;AAAA,EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM;AAAA,MACJ;AAAA,MACA,WAAW;AAAA,MACX,cAAc;AAAA,MACd;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,wBAAwB,yBAAyB,kBAAkB;AACzE,UAAM,gBAAY,qCAAa,GAAG;AAClC,UAAM,cAAc,EAAE,MAAM,SAAS,KAAK,WAAW,GAAG,iBAAiB;AACzE,WACE,4CAAC,sBAAmB,OAAO,oBAAoB,aAA0B,UACtE,wBACC;AAAA,MAAkB;AAAA,MAAjB;AAAA,QACC,SAAO;AAAA,QACN,GAAG;AAAA,QACJ;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QAEA,sDAAC,iCAAU,KAAV,EAAe,GAAG,aAAa,KAAK,cAAc;AAAA;AAAA,IACrD,IAEA,4CAAC,iCAAU,KAAV,EAAe,GAAG,aAAa,KAAK,cAAc,GAEvD;AAAA,EAEJ;AACF;AAMA,IAAM,YAAY;AAKlB,IAAM,kBAAkB,aAAAA,QAAM;AAAA,EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,eAAe,2BAA2B,WAAW,MAAM,kBAAkB;AACnF,UAAM,UAAU,sBAAsB,WAAW,MAAM,kBAAkB;AACzE,UAAM,wBAAwB,yBAAyB,MAAM,kBAAkB;AAC/E,UAAM,UAAU,aAAa,MAAM,SAAS,MAAM,KAAK;AACvD,UAAM,WAAW,QAAQ,YAAY,MAAM;AAC3C,UAAM,cAAc,EAAE,GAAG,OAAO,SAAS,SAAS;AAClD,UAAM,MAAM,aAAAA,QAAM,OAAuB,IAAI;AAC7C,WAAO,QAAQ,cACb;AAAA,MAAkB;AAAA,MAAjB;AAAA,QACC,SAAO;AAAA,QACN,GAAG;AAAA,QACJ,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR;AAAA,QAEA,sDAAC,uBAAqB,GAAG,aAAa,KAAK,cAAc;AAAA;AAAA,IAC3D,IAEA,4CAAC,uBAAqB,GAAG,aAAa,KAAK,cAAc;AAAA,EAE7D;AACF;AAEA,gBAAgB,cAAc;AAa9B,IAAM,sBAAsB,aAAAA,QAAM;AAAA,EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,oBAAoB,OAAO,GAAG,UAAU,IAAI;AACpD,UAAM,eAAe,2BAA2B,WAAW,kBAAkB;AAC7E,UAAM,cAAc,EAAE,MAAM,SAAS,gBAAgB,MAAM,SAAS,gBAAgB,OAAU;AAC9F,UAAM,YAAY,aAAa,SAAS,WAAW,cAAc;AACjE,WACE;AAAA,MAAC;AAAA;AAAA,QACE,GAAG;AAAA,QACH,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,iBAAiB,CAAC,YAAY;AAC5B,cAAI,SAAS;AACX,yBAAa,eAAe,KAAK;AAAA,UACnC,OAAO;AACL,yBAAa,iBAAiB,KAAK;AAAA,UACrC;AAAA,QACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAIA,IAAME,QAAO;AACb,IAAMC,QAAO;", "names": ["<PERSON><PERSON>", "Root", "React", "value", "Root", "<PERSON><PERSON>"]}