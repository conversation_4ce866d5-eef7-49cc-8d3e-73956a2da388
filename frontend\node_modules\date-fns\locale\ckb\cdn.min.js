(()=>{var B;function I(C){return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},I(C)}function x(C,G){var H=Object.keys(C);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(C);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(C,X).enumerable})),H.push.apply(H,J)}return H}function q(C){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?x(Object(H),!0).forEach(function(J){E(C,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(C,Object.getOwnPropertyDescriptors(H)):x(Object(H)).forEach(function(J){Object.defineProperty(C,J,Object.getOwnPropertyDescriptor(H,J))})}return C}function E(C,G,H){if(G=N(G),G in C)Object.defineProperty(C,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else C[G]=H;return C}function N(C){var G=z(C,"string");return I(G)=="symbol"?G:String(G)}function z(C,G){if(I(C)!="object"||!C)return C;var H=C[Symbol.toPrimitive];if(H!==void 0){var J=H.call(C,G||"default");if(I(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(C)}var W=Object.defineProperty,HC=function C(G,H){for(var J in H)W(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})},D={lessThanXSeconds:{one:"\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 \u06CC\u06D5\u06A9 \u0686\u0631\u06A9\u06D5",other:"\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 {{count}} \u0686\u0631\u06A9\u06D5"},xSeconds:{one:"1 \u0686\u0631\u06A9\u06D5",other:"{{count}} \u0686\u0631\u06A9\u06D5"},halfAMinute:"\u0646\u06CC\u0648 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631",lessThanXMinutes:{one:"\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 \u06CC\u06D5\u06A9 \u062E\u0648\u0644\u06D5\u06A9",other:"\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 {{count}} \u062E\u0648\u0644\u06D5\u06A9"},xMinutes:{one:"1 \u062E\u0648\u0644\u06D5\u06A9",other:"{{count}} \u062E\u0648\u0644\u06D5\u06A9"},aboutXHours:{one:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC 1 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631",other:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631"},xHours:{one:"1 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631",other:"{{count}} \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631"},xDays:{one:"1 \u0695\u06C6\u0698",other:"{{count}} \u0698\u06C6\u0698"},aboutXWeeks:{one:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC 1 \u0647\u06D5\u0641\u062A\u06D5",other:"\u062F\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u0647\u06D5\u0641\u062A\u06D5"},xWeeks:{one:"1 \u0647\u06D5\u0641\u062A\u06D5",other:"{{count}} \u0647\u06D5\u0641\u062A\u06D5"},aboutXMonths:{one:"\u062F\u0627\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC 1 \u0645\u0627\u0646\u06AF",other:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u0645\u0627\u0646\u06AF"},xMonths:{one:"1 \u0645\u0627\u0646\u06AF",other:"{{count}} \u0645\u0627\u0646\u06AF"},aboutXYears:{one:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC  1 \u0633\u0627\u06B5",other:"\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u0633\u0627\u06B5"},xYears:{one:"1 \u0633\u0627\u06B5",other:"{{count}} \u0633\u0627\u06B5"},overXYears:{one:"\u0632\u06CC\u0627\u062A\u0631 \u0644\u06D5 \u0633\u0627\u06B5\u06CE\u06A9",other:"\u0632\u06CC\u0627\u062A\u0631 \u0644\u06D5 {{count}} \u0633\u0627\u06B5"},almostXYears:{one:"\u0628\u06D5\u0646\u0632\u06CC\u06A9\u06D5\u06CC\u06CC \u0633\u0627\u06B5\u06CE\u06A9  ",other:"\u0628\u06D5\u0646\u0632\u06CC\u06A9\u06D5\u06CC\u06CC {{count}} \u0633\u0627\u06B5"}},S=function C(G,H,J){var X,Y=D[G];if(typeof Y==="string")X=Y;else if(H===1)X=Y.one;else X=Y.other.replace("{{count}}",H.toString());if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"\u0644\u06D5 \u0645\u0627\u0648\u06D5\u06CC "+X+"\u062F\u0627";else return X+"\u067E\u06CE\u0634 \u0626\u06CE\u0633\u062A\u0627";return X};function $(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):C.defaultWidth,J=C.formats[H]||C.formats[C.defaultWidth];return J}}var M={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' {{time}}",long:"{{date}} '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"'\u0647\u06D5\u0641\u062A\u06D5\u06CC \u0695\u0627\u0628\u0631\u062F\u0648\u0648' eeee '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",yesterday:"'\u062F\u0648\u06CE\u0646\u06CE \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",today:"'\u0626\u06D5\u0645\u0695\u06C6 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",tomorrow:"'\u0628\u06D5\u06CC\u0627\u0646\u06CC \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",nextWeek:"eeee '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",other:"P"},w=function C(G,H,J,X){return j[G]};function O(C){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=C.formattingValues[Z]||C.formattingValues[Y]}else{var T=C.defaultWidth,A=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;X=C.values[A]||C.values[T]}var U=C.argumentCallback?C.argumentCallback(G):G;return X[U]}}var _={narrow:["\u067E","\u062F"],abbreviated:["\u067E-\u0632","\u062F-\u0632"],wide:["\u067E\u06CE\u0634 \u0632\u0627\u06CC\u0646","\u062F\u0648\u0627\u06CC \u0632\u0627\u06CC\u0646"]},f={narrow:["1","2","3","4"],abbreviated:["\u06861\u0645","\u06862\u0645","\u06863\u0645","\u06864\u0645"],wide:["\u0686\u0627\u0631\u06D5\u06AF\u06CC \u06CC\u06D5\u06A9\u06D5\u0645","\u0686\u0627\u0631\u06D5\u06AF\u06CC \u062F\u0648\u0648\u06D5\u0645","\u0686\u0627\u0631\u06D5\u06AF\u06CC \u0633\u06CE\u06CC\u06D5\u0645","\u0686\u0627\u0631\u06D5\u06AF\u06CC \u0686\u0648\u0627\u0631\u06D5\u0645"]},F={narrow:["\u06A9-\u062F","\u0634","\u0626\u0627","\u0646","\u0645","\u062D","\u062A","\u0626\u0627","\u0626\u06D5","\u062A\u0634-\u06CC","\u062A\u0634-\u062F","\u06A9-\u06CC"],abbreviated:["\u06A9\u0627\u0646-\u062F\u0648\u0648","\u0634\u0648\u0628","\u0626\u0627\u062F","\u0646\u06CC\u0633","\u0645\u0627\u06CC\u0633","\u062D\u0648\u0632","\u062A\u06D5\u0645","\u0626\u0627\u0628","\u0626\u06D5\u0644","\u062A\u0634-\u06CC\u06D5\u06A9","\u062A\u0634-\u062F\u0648\u0648","\u06A9\u0627\u0646-\u06CC\u06D5\u06A9"],wide:["\u06A9\u0627\u0646\u0648\u0648\u0646\u06CC \u062F\u0648\u0648\u06D5\u0645","\u0634\u0648\u0628\u0627\u062A","\u0626\u0627\u062F\u0627\u0631","\u0646\u06CC\u0633\u0627\u0646","\u0645\u0627\u06CC\u0633","\u062D\u0648\u0632\u06D5\u06CC\u0631\u0627\u0646","\u062A\u06D5\u0645\u0645\u0648\u0632","\u0626\u0627\u0628","\u0626\u06D5\u06CC\u0644\u0648\u0644","\u062A\u0634\u0631\u06CC\u0646\u06CC \u06CC\u06D5\u06A9\u06D5\u0645","\u062A\u0634\u0631\u06CC\u0646\u06CC \u062F\u0648\u0648\u06D5\u0645","\u06A9\u0627\u0646\u0648\u0648\u0646\u06CC \u06CC\u06D5\u06A9\u06D5\u0645"]},v={narrow:["\u06CC-\u0634","\u062F-\u0634","\u0633-\u0634","\u0686-\u0634","\u067E-\u0634","\u0647\u06D5","\u0634"],short:["\u06CC\u06D5-\u0634\u06D5","\u062F\u0648\u0648-\u0634\u06D5","\u0633\u06CE-\u0634\u06D5","\u0686\u0648-\u0634\u06D5","\u067E\u06CE-\u0634\u06D5","\u0647\u06D5\u06CC","\u0634\u06D5"],abbreviated:["\u06CC\u06D5\u06A9-\u0634\u06D5\u0645","\u062F\u0648\u0648-\u0634\u06D5\u0645","\u0633\u06CE-\u0634\u06D5\u0645","\u0686\u0648\u0627\u0631-\u0634\u06D5\u0645","\u067E\u06CE\u0646\u062C-\u0634\u06D5\u0645","\u0647\u06D5\u06CC\u0646\u06CC","\u0634\u06D5\u0645\u06D5"],wide:["\u06CC\u06D5\u06A9 \u0634\u06D5\u0645\u06D5","\u062F\u0648\u0648 \u0634\u06D5\u0645\u06D5","\u0633\u06CE \u0634\u06D5\u0645\u06D5","\u0686\u0648\u0627\u0631 \u0634\u06D5\u0645\u06D5","\u067E\u06CE\u0646\u062C \u0634\u06D5\u0645\u06D5","\u0647\u06D5\u06CC\u0646\u06CC","\u0634\u06D5\u0645\u06D5"]},P={narrow:{am:"\u067E",pm:"\u062F",midnight:"\u0646-\u0634",noon:"\u0646",morning:"\u0628\u06D5\u06CC\u0627\u0646\u06CC",afternoon:"\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",evening:"\u0626\u06CE\u0648\u0627\u0631\u06D5",night:"\u0634\u06D5\u0648"},abbreviated:{am:"\u067E-\u0646",pm:"\u062F-\u0646",midnight:"\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",noon:"\u0646\u06CC\u0648\u06D5\u0695\u06C6",morning:"\u0628\u06D5\u06CC\u0627\u0646\u06CC",afternoon:"\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",evening:"\u0626\u06CE\u0648\u0627\u0631\u06D5",night:"\u0634\u06D5\u0648"},wide:{am:"\u067E\u06CE\u0634 \u0646\u06CC\u0648\u06D5\u0695\u06C6",pm:"\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",midnight:"\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",noon:"\u0646\u06CC\u0648\u06D5\u0695\u06C6",morning:"\u0628\u06D5\u06CC\u0627\u0646\u06CC",afternoon:"\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",evening:"\u0626\u06CE\u0648\u0627\u0631\u06D5",night:"\u0634\u06D5\u0648"}},k={narrow:{am:"\u067E",pm:"\u062F",midnight:"\u0646-\u0634",noon:"\u0646",morning:"\u0644\u06D5 \u0628\u06D5\u06CC\u0627\u0646\u06CC\u062F\u0627",afternoon:"\u0644\u06D5 \u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6\u062F\u0627",evening:"\u0644\u06D5 \u0626\u06CE\u0648\u0627\u0631\u06D5\u062F\u0627",night:"\u0644\u06D5 \u0634\u06D5\u0648\u062F\u0627"},abbreviated:{am:"\u067E-\u0646",pm:"\u062F-\u0646",midnight:"\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",noon:"\u0646\u06CC\u0648\u06D5\u0695\u06C6",morning:"\u0644\u06D5 \u0628\u06D5\u06CC\u0627\u0646\u06CC\u062F\u0627",afternoon:"\u0644\u06D5 \u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6\u062F\u0627",evening:"\u0644\u06D5 \u0626\u06CE\u0648\u0627\u0631\u06D5\u062F\u0627",night:"\u0644\u06D5 \u0634\u06D5\u0648\u062F\u0627"},wide:{am:"\u067E\u06CE\u0634 \u0646\u06CC\u0648\u06D5\u0695\u06C6",pm:"\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",midnight:"\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",noon:"\u0646\u06CC\u0648\u06D5\u0695\u06C6",morning:"\u0644\u06D5 \u0628\u06D5\u06CC\u0627\u0646\u06CC\u062F\u0627",afternoon:"\u0644\u06D5 \u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6\u062F\u0627",evening:"\u0644\u06D5 \u0626\u06CE\u0648\u0627\u0631\u06D5\u062F\u0627",night:"\u0644\u06D5 \u0634\u06D5\u0648\u062F\u0627"}},b=function C(G,H){return String(G)},h={ordinalNumber:b,era:O({values:_,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:O({values:F,defaultWidth:"wide"}),day:O({values:v,defaultWidth:"wide"}),dayPeriod:O({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function Q(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&C.matchPatterns[J]||C.matchPatterns[C.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],T=J&&C.parsePatterns[J]||C.parsePatterns[C.defaultParseWidth],A=Array.isArray(T)?y(T,function(K){return K.test(Z)}):m(T,function(K){return K.test(Z)}),U;U=C.valueCallback?C.valueCallback(A):A,U=H.valueCallback?H.valueCallback(U):U;var GC=G.slice(Z.length);return{value:U,rest:GC}}}function m(C,G){for(var H in C)if(Object.prototype.hasOwnProperty.call(C,H)&&G(C[H]))return H;return}function y(C,G){for(var H=0;H<C.length;H++)if(G(C[H]))return H;return}function c(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(C.matchPattern);if(!J)return null;var X=J[0],Y=G.match(C.parsePattern);if(!Y)return null;var Z=C.valueCallback?C.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var T=G.slice(X.length);return{value:Z,rest:T}}}var p=/^(\d+)(th|st|nd|rd)?/i,d=/\d+/i,g={narrow:/^(پ|د)/i,abbreviated:/^(پ-ز|د.ز)/i,wide:/^(پێش زاین| دوای زاین)/i},u={any:[/^د/g,/^پ/g]},l={narrow:/^[1234]/i,abbreviated:/^م[1234]چ/i,wide:/^(یەکەم|دووەم|سێیەم| چوارەم) (چارەگی)? quarter/i},i={wide:[/چارەگی یەکەم/,/چارەگی دووەم/,/چارەگی سيیەم/,/چارەگی چوارەم/],any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^(ک-د|ش|ئا|ن|م|ح|ت|ئە|تش-ی|تش-د|ک-ی)/i,abbreviated:/^(کان-دوو|شوب|ئاد|نیس|مایس|حوز|تەم|ئاب|ئەل|تش-یەک|تش-دوو|کان-یەک)/i,wide:/^(کانوونی دووەم|شوبات|ئادار|نیسان|مایس|حوزەیران|تەمموز|ئاب|ئەیلول|تشرینی یەکەم|تشرینی دووەم|کانوونی یەکەم)/i},s={narrow:[/^ک-د/i,/^ش/i,/^ئا/i,/^ن/i,/^م/i,/^ح/i,/^ت/i,/^ئا/i,/^ئە/i,/^تش-ی/i,/^تش-د/i,/^ک-ی/i],any:[/^کان-دوو/i,/^شوب/i,/^ئاد/i,/^نیس/i,/^مایس/i,/^حوز/i,/^تەم/i,/^ئاب/i,/^ئەل/i,/^تش-یەک/i,/^تش-دوو/i,/^|کان-یەک/i]},o={narrow:/^(ش|ی|د|س|چ|پ|هە)/i,short:/^(یە-شە|دوو-شە|سێ-شە|چو-شە|پێ-شە|هە|شە)/i,abbreviated:/^(یەک-شەم|دوو-شەم|سێ-شەم|چوار-شەم|پێنخ-شەم|هەینی|شەمە)/i,wide:/^(یەک شەمە|دوو شەمە|سێ شەمە|چوار شەمە|پێنج شەمە|هەینی|شەمە)/i},r={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},a={narrow:/^(پ|د|ن-ش|ن| (بەیانی|دوای نیوەڕۆ|ئێوارە|شەو))/i,abbreviated:/^(پ-ن|د-ن|نیوە شەو|نیوەڕۆ|بەیانی|دوای نیوەڕۆ|ئێوارە|شەو)/,wide:/^(پێش نیوەڕۆ|دوای نیوەڕۆ|نیوەڕۆ|نیوە شەو|لەبەیانیدا|لەدواینیوەڕۆدا|لە ئێوارەدا|لە شەودا)/,any:/^(پ|د|بەیانی|نیوەڕۆ|ئێوارە|شەو)/},e={any:{am:/^د/i,pm:/^پ/i,midnight:/^ن-ش/i,noon:/^ن/i,morning:/بەیانی/i,afternoon:/دواینیوەڕۆ/i,evening:/ئێوارە/i,night:/شەو/i}},t={ordinalNumber:c({matchPattern:p,parsePattern:d,valueCallback:function C(G){return parseInt(G,10)}}),era:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:Q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},CC={code:"ckb",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},(B=window.dateFns)===null||B===void 0?void 0:B.locale),{},{ckb:CC})})})();

//# debugId=B839470DC90CBAEC64756E2164756E21
