import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Crown, 
  Users, 
  Settings,
  Zap,
  DollarSign,
  AlertTriangle,
  Plus,
  Edit,
  Trash2,
  Eye,
  Shield,
  BarChart3,
  Globe,
  Bell
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

const AdminPanel = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState(null);

  const [subscriptions, setSubscriptions] = useState([
    { id: 1, name: 'Free Package', price: 0, duration: 'Lifetime', maxTime: 180, concurrents: 1, premium: false, api: false },
    { id: 2, name: 'Premium #1', price: 180, duration: 'Monthly', maxTime: 1200, concurrents: 2, premium: true, api: true },
    { id: 3, name: 'Premium #5', price: 900, duration: 'Monthly', maxTime: 7200, concurrents: 10, premium: true, api: true }
  ]);

  const [attackMethods, setAttackMethods] = useState([
    { id: 1, name: 'HTTP-BYPASS', description: 'HTTP Layer 7 bypass method', layer: 'Layer 7', status: 'active' },
    { id: 2, name: 'HTTPS-FLOOD', description: 'HTTPS flooding attack', layer: 'Layer 7', status: 'active' },
    { id: 3, name: 'UDP-FLOOD', description: 'UDP Layer 4 flooding', layer: 'Layer 4', status: 'active' },
    { id: 4, name: 'TCP-SYN', description: 'TCP SYN flood attack', layer: 'Layer 4', status: 'active' }
  ]);

  const [siteSettings, setSiteSettings] = useState({
    siteName: 'STRESSER.BA',
    siteDescription: 'Professional Network Testing Platform',
    keywords: 'stresser, ddos, network testing, security',
    logo: '/logo.png',
    maintenanceMode: false,
    registrationEnabled: true
  });

  const [alerts, setAlerts] = useState([
    { id: 1, title: 'System Maintenance', message: 'Scheduled maintenance on Sunday 2AM-4AM UTC', type: 'info', active: true },
    { id: 2, title: 'New Features', message: 'API v2 is now available for premium users', type: 'success', active: true }
  ]);

  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalAttacks: 0,
    revenue: 0
  });

  // Fetch users from API
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/admin/users`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();
      if (data.success) {
        setUsers(data.data.users);
      }
    } catch (err) {
      setError(err.message);
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch dashboard stats
  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/admin/dashboard`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch stats');
      }

      const data = await response.json();
      if (data.success) {
        setStats(data.data.stats);
      }
    } catch (err) {
      console.error('Error fetching stats:', err);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchUsers();
    fetchStats();
  }, []);

  // User management functions
  const handleViewUser = (user) => {
    setSelectedUser(user);
    setIsViewDialogOpen(true);
  };

  const handleEditUser = (user) => {
    setEditingUser({ ...user });
    setIsEditDialogOpen(true);
  };

  const handleUpdateUser = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/admin/users/${editingUser._id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(editingUser)
      });

      if (!response.ok) {
        throw new Error('Failed to update user');
      }

      const data = await response.json();
      if (data.success) {
        // Update user in local state
        setUsers(prev => prev.map(user =>
          user._id === editingUser._id ? data.data : user
        ));
        setIsEditDialogOpen(false);
        setEditingUser(null);
        alert('User updated successfully');
      }
    } catch (err) {
      alert('Error updating user: ' + err.message);
    }
  };

  const handleDeleteUser = async (userId) => {
    if (!window.confirm('Are you sure you want to delete this user?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/admin/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete user');
      }

      const data = await response.json();
      if (data.success) {
        // Remove user from local state
        setUsers(prev => prev.filter(user => user._id !== userId));
        alert('User deleted successfully');
      }
    } catch (err) {
      alert('Error deleting user: ' + err.message);
    }
  };

  const [newSubscription, setNewSubscription] = useState({
    name: '',
    price: '',
    duration: 'Monthly',
    maxTime: '',
    concurrents: '',
    premium: false,
    api: false
  });

  const [newMethod, setNewMethod] = useState({
    name: '',
    description: '',
    layer: 'Layer 4',
    command: ''
  });

  const [newAlert, setNewAlert] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  const [editingAlert, setEditingAlert] = useState(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  if (!user || user.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
            <p className="text-muted-foreground">You don't have permission to access the admin panel.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleCreateSubscription = () => {
    const subscription = {
      id: Date.now(),
      ...newSubscription,
      price: parseFloat(newSubscription.price),
      maxTime: parseInt(newSubscription.maxTime),
      concurrents: parseInt(newSubscription.concurrents)
    };
    setSubscriptions(prev => [...prev, subscription]);
    setNewSubscription({
      name: '',
      price: '',
      duration: 'Monthly',
      maxTime: '',
      concurrents: '',
      premium: false,
      api: false
    });
  };

  const handleCreateMethod = () => {
    const method = {
      id: Date.now(),
      ...newMethod,
      status: 'active'
    };
    setAttackMethods(prev => [...prev, method]);
    setNewMethod({
      name: '',
      description: '',
      layer: 'Layer 4',
      command: ''
    });
  };

  const handleCreateAlert = () => {
    const alert = {
      id: Date.now(),
      ...newAlert,
      active: true
    };
    setAlerts(prev => [...prev, alert]);
    setNewAlert({
      title: '',
      message: '',
      type: 'info'
    });
  };

  const handleEditAlert = (alert) => {
    setEditingAlert({ ...alert });
    setIsEditDialogOpen(true);
  };

  const handleUpdateAlert = () => {
    setAlerts(prev => prev.map(alert =>
      alert.id === editingAlert.id ? editingAlert : alert
    ));
    setEditingAlert(null);
    setIsEditDialogOpen(false);
  };

  const handleDeleteAlert = (alertId) => {
    if (window.confirm('Are you sure you want to delete this alert?')) {
      setAlerts(prev => prev.filter(alert => alert.id !== alertId));
    }
  };

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold flex items-center space-x-2">
                <Crown className="h-8 w-8 text-primary" />
                <span>Admin Panel</span>
              </h1>
              <p className="text-muted-foreground">Manage your platform</p>
            </div>
            <Badge variant="secondary" className="flex items-center space-x-1">
              <Shield className="h-3 w-3" />
              <span>Administrator</span>
            </Badge>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                  <p className="text-2xl font-bold">{stats.totalUsers.toLocaleString()}</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Users</p>
                  <p className="text-2xl font-bold">{stats.activeUsers.toLocaleString()}</p>
                </div>
                <Globe className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Attacks</p>
                  <p className="text-2xl font-bold">{stats.totalAttacks.toLocaleString()}</p>
                </div>
                <Zap className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Revenue</p>
                  <p className="text-2xl font-bold">€{stats.revenue.toLocaleString()}</p>
                </div>
                <DollarSign className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="users" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="subscriptions">Plans</TabsTrigger>
            <TabsTrigger value="methods">Methods</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="alerts">Alerts</TabsTrigger>
            <TabsTrigger value="stats">Analytics</TabsTrigger>
          </TabsList>

          {/* Users Management */}
          <TabsContent value="users">
            <Card>
              <CardHeader>
                <CardTitle>User Management</CardTitle>
                <CardDescription>Manage user accounts and permissions</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                      <p className="text-muted-foreground">Loading users...</p>
                    </div>
                  </div>
                ) : error ? (
                  <div className="text-center py-8">
                    <p className="text-red-500 mb-4">Error: {error}</p>
                    <Button onClick={fetchUsers} variant="outline">
                      Try Again
                    </Button>
                  </div>
                ) : users.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No users found</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {users.map((user) => (
                      <div key={user._id || user.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div>
                            <p className="font-medium">{user.username}</p>
                            <p className="text-sm text-muted-foreground">{user.email}</p>
                          </div>
                          <Badge variant={user.role === 'admin' ? 'default' : user.role === 'reseller' ? 'secondary' : 'outline'}>
                            {user.role}
                          </Badge>
                          <Badge variant="outline">
                            {user.subscription_id?.name || 'No Subscription'}
                          </Badge>
                          <Badge variant={user.is_active ? 'default' : 'destructive'}>
                            {user.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="outline" onClick={() => handleViewUser(user)}>
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleEditUser(user)}>
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeleteUser(user._id || user.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Subscriptions Management */}
          <TabsContent value="subscriptions">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Subscription Plans</CardTitle>
                      <CardDescription>Manage pricing plans and features</CardDescription>
                    </div>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button>
                          <Plus className="h-4 w-4 mr-2" />
                          Add Plan
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Create New Plan</DialogTitle>
                          <DialogDescription>Add a new subscription plan</DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Plan Name</Label>
                              <Input
                                value={newSubscription.name}
                                onChange={(e) => setNewSubscription(prev => ({ ...prev, name: e.target.value }))}
                                placeholder="Premium #3"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Price (€)</Label>
                              <Input
                                type="number"
                                value={newSubscription.price}
                                onChange={(e) => setNewSubscription(prev => ({ ...prev, price: e.target.value }))}
                                placeholder="300"
                              />
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Max Time (seconds)</Label>
                              <Input
                                type="number"
                                value={newSubscription.maxTime}
                                onChange={(e) => setNewSubscription(prev => ({ ...prev, maxTime: e.target.value }))}
                                placeholder="3600"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Concurrents</Label>
                              <Input
                                type="number"
                                value={newSubscription.concurrents}
                                onChange={(e) => setNewSubscription(prev => ({ ...prev, concurrents: e.target.value }))}
                                placeholder="5"
                              />
                            </div>
                          </div>
                          <Button onClick={handleCreateSubscription} className="w-full">
                            Create Plan
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {subscriptions.map((plan) => (
                      <Card key={plan.id} className="border-2">
                        <CardHeader className="pb-4">
                          <CardTitle className="text-lg">{plan.name}</CardTitle>
                          <div className="text-2xl font-bold">€{plan.price}</div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>Max Time:</span>
                              <span>{plan.maxTime}s</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Concurrents:</span>
                              <span>{plan.concurrents}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Premium:</span>
                              <span>{plan.premium ? '✅' : '❌'}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>API:</span>
                              <span>{plan.api ? '✅' : '❌'}</span>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline" className="flex-1">
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            <Button size="sm" variant="outline" className="flex-1">
                              <Trash2 className="h-3 w-3 mr-1" />
                              Delete
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Attack Methods */}
          <TabsContent value="methods">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Attack Methods</CardTitle>
                    <CardDescription>Manage available attack methods</CardDescription>
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Method
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create New Method</DialogTitle>
                        <DialogDescription>Add a new attack method</DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label>Method Name</Label>
                          <Input
                            value={newMethod.name}
                            onChange={(e) => setNewMethod(prev => ({ ...prev, name: e.target.value }))}
                            placeholder="HTTP-CUSTOM"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Description</Label>
                          <Input
                            value={newMethod.description}
                            onChange={(e) => setNewMethod(prev => ({ ...prev, description: e.target.value }))}
                            placeholder="Custom HTTP attack method"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Layer</Label>
                          <Select value={newMethod.layer} onValueChange={(value) => setNewMethod(prev => ({ ...prev, layer: value }))}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Layer 4">Layer 4</SelectItem>
                              <SelectItem value="Layer 7">Layer 7</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>Command String</Label>
                          <Textarea
                            value={newMethod.command}
                            onChange={(e) => setNewMethod(prev => ({ ...prev, command: e.target.value }))}
                            placeholder="attack_command --target {target} --port {port} --time {time}"
                          />
                        </div>
                        <Button onClick={handleCreateMethod} className="w-full">
                          Create Method
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {attackMethods.map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div>
                          <p className="font-medium">{method.name}</p>
                          <p className="text-sm text-muted-foreground">{method.description}</p>
                        </div>
                        <Badge variant="outline">
                          {method.layer}
                        </Badge>
                        <Badge variant={method.status === 'active' ? 'default' : 'secondary'}>
                          {method.status}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button size="sm" variant="outline">
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button size="sm" variant="outline">
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Site Settings */}
          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Site Settings</CardTitle>
                <CardDescription>Configure global site settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label>Site Name</Label>
                    <Input
                      value={siteSettings.siteName}
                      onChange={(e) => setSiteSettings(prev => ({ ...prev, siteName: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Logo URL</Label>
                    <Input
                      value={siteSettings.logo}
                      onChange={(e) => setSiteSettings(prev => ({ ...prev, logo: e.target.value }))}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Site Description</Label>
                  <Textarea
                    value={siteSettings.siteDescription}
                    onChange={(e) => setSiteSettings(prev => ({ ...prev, siteDescription: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>SEO Keywords</Label>
                  <Input
                    value={siteSettings.keywords}
                    onChange={(e) => setSiteSettings(prev => ({ ...prev, keywords: e.target.value }))}
                  />
                </div>
                <Button>Save Settings</Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Alerts */}
          <TabsContent value="alerts">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Site Alerts</CardTitle>
                    <CardDescription>Manage site-wide notifications</CardDescription>
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Alert
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create New Alert</DialogTitle>
                        <DialogDescription>Add a new site alert</DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label>Title</Label>
                          <Input
                            value={newAlert.title}
                            onChange={(e) => setNewAlert(prev => ({ ...prev, title: e.target.value }))}
                            placeholder="Alert title"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Message</Label>
                          <Textarea
                            value={newAlert.message}
                            onChange={(e) => setNewAlert(prev => ({ ...prev, message: e.target.value }))}
                            placeholder="Alert message"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Type</Label>
                          <Select value={newAlert.type} onValueChange={(value) => setNewAlert(prev => ({ ...prev, type: value }))}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="info">Info</SelectItem>
                              <SelectItem value="success">Success</SelectItem>
                              <SelectItem value="warning">Warning</SelectItem>
                              <SelectItem value="error">Error</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <Button onClick={handleCreateAlert} className="w-full">
                          Create Alert
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>

                  {/* Edit Alert Dialog */}
                  <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Edit Alert</DialogTitle>
                        <DialogDescription>Update the alert information</DialogDescription>
                      </DialogHeader>
                      {editingAlert && (
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label>Title</Label>
                            <Input
                              value={editingAlert.title}
                              onChange={(e) => setEditingAlert(prev => ({ ...prev, title: e.target.value }))}
                              placeholder="Alert title"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Message</Label>
                            <Textarea
                              value={editingAlert.message}
                              onChange={(e) => setEditingAlert(prev => ({ ...prev, message: e.target.value }))}
                              placeholder="Alert message"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Type</Label>
                            <Select value={editingAlert.type} onValueChange={(value) => setEditingAlert(prev => ({ ...prev, type: value }))}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="info">Info</SelectItem>
                                <SelectItem value="success">Success</SelectItem>
                                <SelectItem value="warning">Warning</SelectItem>
                                <SelectItem value="error">Error</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label>Status</Label>
                            <Select value={editingAlert.active ? 'active' : 'inactive'} onValueChange={(value) => setEditingAlert(prev => ({ ...prev, active: value === 'active' }))}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <Button onClick={handleUpdateAlert} className="w-full">
                            Update Alert
                          </Button>
                        </div>
                      )}
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {alerts.map((alert) => (
                    <div key={alert.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <Bell className={`h-5 w-5 ${
                          alert.type === 'success' ? 'text-green-500' :
                          alert.type === 'warning' ? 'text-yellow-500' :
                          alert.type === 'error' ? 'text-red-500' : 'text-blue-500'
                        }`} />
                        <div>
                          <p className="font-medium">{alert.title}</p>
                          <p className="text-sm text-muted-foreground">{alert.message}</p>
                        </div>
                        <Badge variant={alert.active ? 'default' : 'secondary'}>
                          {alert.active ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button size="sm" variant="outline" onClick={() => handleEditAlert(alert)}>
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleDeleteAlert(alert.id)}>
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics */}
          <TabsContent value="stats">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Platform Analytics</CardTitle>
                  <CardDescription>System performance and usage statistics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Analytics Dashboard</h3>
                    <p className="text-muted-foreground">
                      Detailed analytics and reporting features will be implemented here.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* User View Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>User Details</DialogTitle>
              <DialogDescription>View user information</DialogDescription>
            </DialogHeader>
            {selectedUser && (
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Username</Label>
                  <p className="text-sm text-muted-foreground">{selectedUser.username}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Email</Label>
                  <p className="text-sm text-muted-foreground">{selectedUser.email}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Role</Label>
                  <p className="text-sm text-muted-foreground">{selectedUser.role}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <p className="text-sm text-muted-foreground">
                    {selectedUser.is_active ? 'Active' : 'Inactive'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Subscription</Label>
                  <p className="text-sm text-muted-foreground">
                    {selectedUser.subscription_id?.name || 'No Subscription'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Created At</Label>
                  <p className="text-sm text-muted-foreground">
                    {selectedUser.createdAt ? new Date(selectedUser.createdAt).toLocaleDateString() : 'N/A'}
                  </p>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* User Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Edit User</DialogTitle>
              <DialogDescription>Update user information</DialogDescription>
            </DialogHeader>
            {editingUser && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-username">Username</Label>
                  <Input
                    id="edit-username"
                    value={editingUser.username}
                    onChange={(e) => setEditingUser(prev => ({ ...prev, username: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-email">Email</Label>
                  <Input
                    id="edit-email"
                    type="email"
                    value={editingUser.email}
                    onChange={(e) => setEditingUser(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-role">Role</Label>
                  <Select
                    value={editingUser.role}
                    onValueChange={(value) => setEditingUser(prev => ({ ...prev, role: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">User</SelectItem>
                      <SelectItem value="reseller">Reseller</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-status">Status</Label>
                  <Select
                    value={editingUser.is_active ? 'active' : 'inactive'}
                    onValueChange={(value) => setEditingUser(prev => ({ ...prev, is_active: value === 'active' }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end space-x-2 pt-4">
                  <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleUpdateUser}>
                    Update User
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default AdminPanel;

